<?php

declare(strict_types=1);

namespace App\Tests\Functional\Service;

use App\Entity\Setting;
use App\Service\SettingsService;
use App\Tests\Functional\FunctionalTestCase;
use PHPUnit\Framework\Attributes\DataProvider;

class SettingsServiceFunctionalTest extends FunctionalTestCase
{
    private const string EXPECTED_LOCALE = 'es';

    private SettingsService $settingsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->settingsService = static::getContainer()->get(SettingsService::class);
    }

    /**
     * Tests settings configuration sources and fallback mechanism.
     */
    #[DataProvider('settingsConfigurationProvider')]
    public function testSettingsConfiguration(string $configKey, $expectedValue, bool $shouldExistInDatabase, string $description): void
    {
        $settingRepository = $this->getRepository(Setting::class);

        // Verify database existence expectation
        $settingFromDb = $settingRepository->findOneBy(['code' => $configKey]);

        if ($shouldExistInDatabase) {
            $this->assertNotNull($settingFromDb, "{$configKey} should exist in database (fixtures)");
            $this->assertEquals($expectedValue, $settingFromDb->getValue(), "{$configKey} should have correct value in database");
        } else {
            $this->assertNull($settingFromDb, "{$configKey} should not exist in database (to test YAML fallback)");
        }

        // Test SettingsService retrieval
        $actualValue = $this->settingsService->get($configKey);

        if (null === $expectedValue) {
            $this->assertNull($actualValue, $description);
        } else {
            $this->assertEquals($expectedValue, $actualValue, $description);
        }
    }

    public static function settingsConfigurationProvider(): array
    {
        return [
            'app.defaultLanguage from fixtures' => [
                'configKey' => 'app.defaultLanguage',
                'expectedValue' => self::EXPECTED_LOCALE,
                'shouldExistInDatabase' => true,
                'description' => 'app.defaultLanguage should exist in fixtures and return es (Campus context)',
            ],
            'app.adminDefaultLanguage from fixtures' => [
                'configKey' => 'app.adminDefaultLanguage',
                'expectedValue' => self::EXPECTED_LOCALE,
                'shouldExistInDatabase' => true,
                'description' => 'app.adminDefaultLanguage should exist in fixtures and return es (Admin context)',
            ],
            'app.saml from YAML fallback' => [
                'configKey' => 'app.saml',
                'expectedValue' => false,
                'shouldExistInDatabase' => false,
                'description' => 'app.saml should not exist in fixtures but return false from YAML fallback',
            ],
            'app.default_locale should not exist' => [
                'configKey' => 'app.default_locale',
                'expectedValue' => null,
                'shouldExistInDatabase' => false,
                'description' => 'The old incorrect app.default_locale should not exist (issue #734 fix)',
            ],
        ];
    }
}
