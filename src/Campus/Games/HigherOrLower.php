<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\HigherLower as HigherLowerEntity;
use App\Entity\HigherLowerWords;
use App\Enum\Games as EnumGameFormula;

class HigherOrLower extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(HigherLowerEntity::class);

        $higherLowersQuestions = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $answersLower = [];

        foreach ($higherLowersQuestions as $higher) {
            $answersLower[] = [
                'id' => $higher->getId(),
                'title' => $higher->getTitle(),
                'questions' => $this->getFormattedQuestions($higher->getHigherLowerWords()),
                'time' => $higher->getTime(),
            ];
        }

        return ['questions' => $answersLower];
    }

    private function getFormattedQuestions($memoryQuestions): array
    {
        $questions = [];
        foreach ($memoryQuestions as $question) {
            $questions[$question->getId()] = [
                'id' => $question->getId(),
                'text' => $question->getWord(),
            ];
        }

        shuffle($questions);

        return $questions;
    }

    public function check($userCourseChapter, $answersFront): array
    {
        $ids = isset($answersFront->ids) ? $answersFront->ids : [];
        $questions = [];

        $valueInitial = \count($ids);
        for ($i = 0; $i < \count($ids); ++$i) {
            $position = $valueInitial - $i;
            $wordPosition = $this->em->getRepository(HigherLowerWords::class)->find($ids[$i]);
            $isCorrect = $wordPosition->getPosition() == $position;

            $questions[] = [
                'id' => $ids[$i],
                'correct' => $isCorrect,
            ];
        }

        return ['attempt' => $questions];
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $higherLowerQuestions = $args->getHigherLowers();
        $nQuestions = \count($higherLowerQuestions);
        $failedResolutions = 0;
        $rightQuestions = 0;
        $time = 0;
        $maxTime = $data['timeTotal'];
        $nCorrectAnswers = 0;
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;

        if (\count($data['answers']) > 0) {
            foreach ($answers as $answer) {
                $nCorrectAnswerItems = 0;
                foreach ($answer['attempt'] as $answerAttempt) {
                    if (isset($answerAttempt['correct'])) {
                        if (!$answerAttempt['correct']) {
                            --$nCorrectAnswerItems;
                            ++$failedResolutions;
                        }
                        if ($answerAttempt['correct']) {
                            ++$nCorrectAnswerItems;
                            ++$rightQuestions;
                        }
                    }
                }

                if ($nCorrectAnswerItems == \count($answer['attempt'])) {
                    ++$nCorrectAnswers;
                }

                $time += $answer['time'];
            }
        }

        if ($time >= $maxTime) {
            return 0;
        }
        $completionPercentage = $rightQuestions > 0 ? ($nCorrectAnswers / $nQuestions) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + EnumGameFormula::BASE_QUARTER * pow(EnumGameFormula::HIGHER_OR_LOWER_PENALTY, $failedResolutions) + EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime);
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return ($adjustedPercentage >= $chapterType->getPercentageCompleted()) ? $attemptsCorrectionPercentage : 0;
    }
}
