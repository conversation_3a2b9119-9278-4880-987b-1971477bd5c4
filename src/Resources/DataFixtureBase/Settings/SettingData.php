<?php

declare(strict_types=1);

namespace App\Resources\DataFixtureBase\Settings;

use App\Enum\SettingGroupCode;

class SettingData
{
    public const DEFAULT_DATA = [
        [
            'id' => 1,
            'group' => 'Platform',
            'code' => SettingGroupCode::PLATFORM,
            'position' => 1,
            'settings' => [
                [
                    'code' => 'app.multilingual',
                    'value' => 'true',
                    'name' => 'setting.multi_idioma.name',
                    'description' => 'setting.multi_idioma.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.defaultLanguage',
                    'value' => 'es',
                    'name' => 'setting.default_lenguage.name',
                    'description' => 'setting.default_lenguage.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.languages',
                    'value' => '["en", "pt", "es", "fr"]',
                    'name' => 'setting.languages.name',
                    'description' => 'setting.languages.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.languages.admin',
                    'value' => '["en", "es"]',
                    'name' => 'setting.lenguage.platform',
                    'description' => 'setting.languages.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.adminDefaultLanguage',
                    'value' => 'es',
                    'name' => 'setting.admin_default_language.name',
                    'description' => 'setting.admin_default_language.description',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.free.user.registration',
                    'value' => 'false',
                    'name' => 'setting.registro_libre.name',
                    'description' => 'setting.registro_libre.description',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.opinions.platform',
                    'value' => 'true',
                    'name' => 'setting.opinion_plataforma.name',
                    'description' => 'setting.opinion_plataforma.description',
                    'sort' => 7,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.free.user.registration-autovalidate',
                    'value' => 'true',
                    'name' => 'setting.validacion_automatica.name',
                    'description' => 'setting.validacion_automatica.description',
                    'sort' => 8,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.user.useFilters',
                    'value' => 'true',
                    'name' => 'setting.filtros_plataforma.name',
                    'description' => 'setting.filtros_plataforma.description',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.use_itinerary',
                    'value' => 'true',
                    'name' => 'setting.itinearios_plataforma.name',
                    'description' => 'setting.itinearios_plataforma.description',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.sections',
                    'value' => '["INFORMATION", "CONTENT","OPINIONS"]',
                    'name' => 'setting.seccion_cursos.name',
                    'description' => 'setting.seccion_cursos.description',
                    'type' => 'array',
                    'sort' => 11,
                    'options' => [],
                ],
                [
                    'code' => 'app.userPolicies',
                    'value' => 'false',
                    'name' => 'setting.userPolicies_plataforma.name',
                    'description' => 'setting.userPolicies_plataforma.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.pages',
                    'value' => 'false',
                    'name' => 'setting.pages.name',
                    'description' => 'setting.pages.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.use.filter_in_ranking',
                    'value' => 'false',
                    'name' => 'setting.use.filter_in_ranking.name',
                    'description' => 'setting.use.filter_in_ranking.description',
                    'sort' => 14,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.filter_include_only_first_category',
                    'value' => 'true',
                    'name' => 'setting.use.include_only_first_category_name',
                    'description' => 'setting.use.include_only_first_category_description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'password.policy.enabled',
                    'value' => 'false',
                    'name' => 'setting.password.policy.name',
                    'description' => 'setting.password_policy.description',
                    'sort' => 16,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'password.minimum',
                    'value' => '3',
                    'name' => 'setting.password.minimum.name',
                    'description' => 'setting.password.minimum.description',
                    'sort' => 17,
                    'options' => [],
                    'type' => 'integer',
                ],
            ],
        ],
        [
            'id' => 2,
            'group' => 'Courses',
            'code' => SettingGroupCode::COURSES,
            'position' => 2,
            'settings' => [
                [
                    'code' => 'app.setCoursePoints',
                    'value' => 'false',
                    'name' => 'setting.set_points_course.name',
                    'description' => 'setting.set_points_course.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.courseDefaultPoints',
                    'value' => '500',
                    'name' => 'setting.default_points_course.name',
                    'description' => 'setting.default_points_course.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.courseInfoGeneral',
                    'value' => 'false',
                    'name' => 'setting.documentation_course.name',
                    'description' => 'setting.documentation_course.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.openCourse',
                    'value' => 'true',
                    'name' => 'setting.open_course.name',
                    'description' => 'setting.open_course.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.tab.person',
                    'value' => 'true',
                    'name' => 'setting.course.tab.person.name',
                    'description' => 'setting.course.tab.person.description',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.tab.stats',
                    'value' => 'true',
                    'name' => 'setting.course.tab.stats.name',
                    'description' => 'setting.course.tab.stats.description',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.tab.opinions',
                    'value' => 'true',
                    'name' => 'setting.course.tab.opinions.name',
                    'description' => 'setting.course.tab.opinions.description',
                    'sort' => 7,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.showDeactivatedCourses',
                    'value' => 'false',
                    'name' => 'setting.course.showDeactivatedCourses.name',
                    'description' => 'setting.course.showDeactivatedCourses.description',
                    'sort' => 8,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.content.diploma',
                    'value' => 'true',
                    'name' => 'course.diploma.index',
                    'description' => 'course.diploma.index',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.filters.diploma',
                    'value' => 'false',
                    'name' => 'course.diploma.filters',
                    'description' => 'course.diploma.filters',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.stats.reports',
                    'value' => 'true',
                    'name' => 'course.stats.reports',
                    'description' => 'course.stats.reports.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
        [
            'id' => 3,
            'group' => 'Announcements',
            'code' => SettingGroupCode::ANNOUNCEMENTS,
            'position' => 3,
            'settings' => [
                [
                    'code' => 'app.fundae',
                    'value' => 'false',
                    'name' => 'setting.fundae.name',
                    'description' => 'setting.fundae.description',
                    'type' => 'bool',
                    'sort' => 30,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.default_entry_margin',
                    'value' => 5,
                    'name' => 'setting.margen_entrada.name',
                    'description' => 'setting.margen_entrada.description',
                    'type' => 'integer',
                    'sort' => 31,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.default_exit_margin',
                    'value' => 5,
                    'name' => 'setting.margen_salida.name',
                    'description' => 'setting.margen_salida.description',
                    'type' => 'integer',
                    'sort' => 32,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.register_on_qr',
                    'value' => 'false',
                    'name' => 'setting.registrar_qr.name',
                    'description' => 'setting.registrar_qr.description',
                    'type' => 'bool',
                    'sort' => 33,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.max_students_per_group',
                    'value' => 80,
                    'name' => 'setting.maximo_alumnos.name',
                    'description' => 'setting.maximo_alumnos.description',
                    'type' => 'integer',
                    'sort' => 34,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.min_passing_score',
                    'value' => 75,
                    'name' => 'setting.min_score.name',
                    'description' => 'setting.min_score.description',
                    'type' => 'integer',
                    'sort' => 35,
                    'options' => [],
                ],
                [
                    'code' => 'app.fundae.action_types',
                    'value' => "['FUNDAE_TRIPARTITA', 'HOBETUZ']",
                    'name' => 'setting.types_action.name',
                    'description' => 'setting.types_action.description',
                    'type' => 'array',
                    'sort' => 36,
                    'options' => [],
                ],
                [
                    'code' => 'app.course.criteria',
                    'value' => '["MAXIMUM_INACTIVITY_TIME": "5"]',
                    'name' => 'setting.minimo_minutos.name',
                    'description' => 'setting.minimo_minutos.description',
                    'type' => 'array',
                    'sort' => 47,
                    'options' => [],
                ],
                [
                    'code' => 'app.default_timezone',
                    'value' => 'Europe/Madrid',
                    'name' => 'setting.timezones_default.name',
                    'description' => 'setting.timezones_default.description',
                    'type' => 'string',
                    'sort' => 49,
                    'options' => [],
                ],
                [
                    'code' => 'app.course.materials.enabled',
                    'value' => 'true',
                    'name' => 'setting.materiales_convocatoria.name',
                    'description' => 'setting.materiales_convocatoria.description',
                    'type' => 'bool',
                    'sort' => 38,
                    'options' => [],
                ],
                [
                    'code' => 'app.course.tasks.enabled',
                    'value' => 'true',
                    'name' => 'setting.tareas_convocatoria.name',
                    'description' => 'setting.tareas_convocatoria.description',
                    'type' => 'bool',
                    'sort' => 39,
                    'options' => [],
                ],
                [
                    'code' => 'app.timezones',
                    'value' => '["Europe\/Madrid"]',
                    'name' => 'setting.timezones.name',
                    'description' => 'setting.timezones.description',
                    'type' => 'array',
                    'sort' => 48,
                    'options' => [],
                ],
                [
                    'code' => 'app.announcement.managers.sharing',
                    'value' => 'false',
                    'name' => 'app.announcement.managers.sharing.name',
                    'description' => 'app.announcement.managers.sharing.description',
                    'type' => 'bool',
                    'sort' => 503,
                    'options' => [],
                ],
            ],
        ],
        [
            'id' => 4,
            'group' => 'General vimeo',
            'code' => SettingGroupCode::VIMEO_GENERAL,
            'position' => 4,
            'settings' => [
                [
                    'code' => 'app.clientIdVimeo',
                    'value' => '13cf8196ce2d72fd53193f50407e4b54fb2a2c77',
                    'name' => 'setting.client_id.name',
                    'description' => 'setting.client_id.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clientSecretVimeo',
                    'value' => 'EdJS8wDCJ4iixDX5ErIwnE6NMrYFe4qEe7tPIqwskeQWmdedceRWOsgcS4Mw9ZfpBIF5uG9lNKnLSBu8WigWp3EcVfoX+cXxlqv54qCbz5CjTYjXxopsPUA/taXAKpMf',
                    'name' => 'setting.client_secret.name',
                    'description' => 'setting.client_secret.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.accessTokenVimeo',
                    'value' => '99e6bb127198b0b3982fb42369abe7e3',
                    'name' => 'setting.access_token.name',
                    'description' => 'setting.access_token.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.userIdVimeo',
                    'value' => '99351134',
                    'name' => 'setting.user_id.name',
                    'description' => 'setting.user_id.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'id' => 5,
            'group' => 'Vimeo Client',
            'code' => SettingGroupCode::VIMEO_CLIENT,
            'position' => 5,
            'settings' => [
                [
                    'code' => 'app.projectIdVimeo',
                    'value' => '15579633',
                    'name' => 'setting.project_id.name',
                    'description' => 'setting.project_id.description',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdResourceCourse',
                    'value' => '15579391',
                    'name' => 'setting.project_id_resource_course.name',
                    'description' => 'setting.project_id_resource_course.description',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdTaskCourse',
                    'value' => '15579531',
                    'name' => 'setting.project_id_task_course.name',
                    'description' => 'setting.project_id_task_course.description',
                    'sort' => 7,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdVideoQuiz',
                    'value' => '15579537',
                    'name' => 'setting.project_id_video_Quiz.name',
                    'description' => 'setting.project_id_video_Quiz.description',
                    'sort' => 8,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdRoleplay',
                    'value' => '17156756',
                    'name' => 'setting.project_id_Roleplay.name',
                    'description' => 'setting.project_id_Roleplay.description',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.uploadSubdomain',
                    'value' => '',
                    'name' => 'setting.upload_sudomain.name',
                    'description' => 'setting.upload_sudomain.description',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'id' => 6,
            'group' => 'Email',
            'code' => SettingGroupCode::EMAIL,
            'position' => 6,
            'settings' => [
                [
                    'code' => 'app.fromEmail',
                    'value' => '<EMAIL>',
                    'name' => 'setting.from_email.name',
                    'description' => 'setting.from_email.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.fromName',
                    'value' => 'Easylearning',
                    'name' => 'setting.from_name.name',
                    'description' => 'setting.from_name.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.fromCIF',
                    'value' => '2342343Y',
                    'name' => 'setting.from_cif.name',
                    'description' => 'setting.from_cif.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.supportEmail',
                    'value' => '["<EMAIL>"]',
                    'name' => 'setting.email_support.name',
                    'description' => 'setting.email_support.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.email.administrator.reception',
                    'value' => '[<EMAIL>]',
                    'name' => 'setting.email_support_register.name',
                    'description' => 'setting.email_support_register.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.email.error.reception',
                    'value' => '["<EMAIL>"]',
                    'name' => 'setting.email_support_error.name',
                    'description' => 'setting.email_support_error.description',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'array',
                ],
            ],
        ],
        [
            'id' => 7,
            'group' => 'Modules',
            'code' => SettingGroupCode::MODULES,
            'position' => 7,
            'settings' => [
                [
                    'code' => 'app.news.enabled',
                    'value' => 'false',
                    'name' => 'setting.news.name',
                    'description' => 'setting.news.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.forum.enabled',
                    'value' => 'false',
                    'name' => 'setting.foro.name',
                    'description' => 'setting.foro.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.challenge.enabled',
                    'value' => 'false',
                    'name' => 'setting.desafios.name',
                    'description' => 'setting.desafios.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.sharefile.enabled',
                    'value' => 'false',
                    'name' => 'setting.sharefile.name',
                    'description' => 'setting.sharefile.description',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course_section.enabled',
                    'value' => 'true',
                    'name' => 'setting.secciones.name',
                    'description' => 'setting.secciones.description',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.survey',
                    'value' => 'true',
                    'name' => 'setting.encuestas.name',
                    'description' => 'setting.encuestas.description',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.documentation.enabled',
                    'value' => 'false',
                    'name' => 'setting.documentation.name',
                    'description' => 'setting.documentation.description',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.user_company.enabled',
                    'value' => 'false',
                    'name' => 'setting.user_company.name',
                    'description' => 'setting.user_company.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.announcement.enabled',
                    'value' => 'true',
                    'name' => 'setting.module.announcement.name',
                    'description' => 'setting.module.announcement.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
        [
            'id' => 8,
            'group' => 'Exports',
            'code' => SettingGroupCode::EXPORTS,
            'position' => 8,
            'settings' => [
                [
                    'code' => 'app.export.active_cron_exports',
                    'value' => 'true',
                    'name' => 'setting.active_cron_exports.name',
                    'description' => 'setting.active_cron_exports.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.export.gender_excel',
                    'value' => 'true',
                    'name' => 'setting.gender_excel.name',
                    'description' => 'setting.gender_excel.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.export.code',
                    'value' => 'true',
                    'name' => 'setting.code.name',
                    'description' => 'setting.code.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.export.finishedChapters',
                    'value' => 'true',
                    'name' => 'setting.finished_chapters.name',
                    'description' => 'setting.finished_chapters.description',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.zip.day_available_until',
                    'value' => '5',
                    'name' => 'setting.zip.day_available_until.name',
                    'description' => 'setting.zip.day_available_until.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.task.slot_quantity',
                    'value' => '3',
                    'name' => 'setting.export.task.slot_quantity.name',
                    'description' => 'setting.export.task.slot_quantity.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.task.long_running_type_tasks',
                    'value' => '["export-stats-file","export-file"]',
                    'name' => 'setting.export.task.long_running_type_tasks.name',
                    'description' => 'setting.export.task.long_running_type_tasks.description',
                    'sort' => 14,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.export.zip_task.slot_quantity',
                    'value' => '3',
                    'name' => 'setting.export.zip_task.slot_quantity.name',
                    'description' => 'setting.export.zip_task.slot_quantity.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.zip_task.long_running_type_tasks',
                    'value' => '["course_perCourseStatsReport","itinerary_perItineraryStatsReport"]',
                    'name' => 'setting.export.zip_task.long_running_type_tasks.name',
                    'description' => 'setting.export.zip_task.long_running_type_tasks.description',
                    'sort' => 16,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.export.task.user_pending_max_count_task',
                    'value' => '10',
                    'name' => 'setting.export.task.user_pending_max_count_task.name',
                    'description' => 'setting.export.task.user_pending_max_count_task.description',
                    'sort' => 17,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.task.timeout',
                    'value' => '600',
                    'name' => 'setting.export.task.timeout.name',
                    'description' => 'setting.export.task.timeout.description',
                    'sort' => 18,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.zip_task.timeout',
                    'value' => '600',
                    'name' => 'setting.export.zip_task.timeout.name',
                    'description' => 'setting.export.zip_task.timeout.description',
                    'sort' => 19,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.export.task.timeout_seconds',
                    'value' => '3600',
                    'name' => 'setting.export.task.timeout_seconds.name',
                    'description' => 'setting.export.task.timeout_seconds.description',
                    'sort' => 20,
                    'options' => [],
                    'type' => 'integer',
                ],
            ],
        ],
        [
            'id' => 9,
            'group' => 'Virtual Classroom',
            'code' => SettingGroupCode::VIRTUAL_CLASSROOM,
            'position' => 9,
            'settings' => [
                [
                    'code' => 'app.zoomClientId',
                    'value' => '4Nz0XjKeRgiHWhPdVBHDZw',
                    'name' => 'setting.zoom_cliente_id.name',
                    'description' => 'setting.zoom_cliente_id.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.zoomClientSecret',
                    'value' => '8vA4dxVJ7HruZvtBHWOPE8U7DvSPo4L0',
                    'name' => 'setting.zoom_cliente_secret.name',
                    'description' => 'setting.zoom_cliente_secret.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.zoomAccountId',
                    'value' => '1X2T4bsSTGKYb6yEQNFPYQ',
                    'name' => 'setting.zoom_account_id.name',
                    'description' => 'setting.zoom_account_id.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.zoomEmail',
                    'value' => '<EMAIL>',
                    'name' => 'setting.zoom_email.name',
                    'description' => 'setting.zoom_email.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clickMeetingApiKey',
                    'value' => 'eue332cce6fb2d9cda1abfccb759c77d6e777a2020',
                    'name' => 'setting.clickmeeting_api_key.name',
                    'description' => 'setting.clickmeeting_api_key.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clickMeetingDirBase',
                    'value' => 'https://easylearning.easylearning.video/',
                    'name' => 'setting.clikmeeting_dirbase.name',
                    'description' => 'setting.clikmeeting_dirbase.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clickMeetingEventsParalel',
                    'value' => 0,
                    'name' => 'setting.clikmeeting_events_paralel.name',
                    'description' => 'setting.clikmeeting_events_paralel.description',
                    'setting_group' => 12,
                    'sort' => 12,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.plugNmeetServerurl',
                    'value' => 'https://edge.plugnmeet.cloud',
                    'name' => 'setting.plugnmeet_serverurl.name',
                    'description' => 'setting.plugnmeet_serverurl.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.plugNmeetApikey',
                    'value' => 'APIX28Tydn3KcOKibN',
                    'name' => 'setting.plugnmeet_api_key.name',
                    'description' => 'setting.plugnmeet_api_key.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.plugNmeetSecret',
                    'value' => '3A8yGHZHtmoZdlbxj0HWfl3c6B2dV7mJ98swgysO',
                    'name' => 'setting.plugnmeet_secret.name',
                    'description' => 'setting.plugnmeet_secret.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.plugNmeetAnalyticsurl',
                    'value' => 'https://edge.plugnmeet.cloud/download/analytics/',
                    'name' => 'setting.plugnmeet_analyticsurl.name',
                    'description' => 'setting.plugnmeet_analyticsurl.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.zoomUrlReports',
                    'value' => 'uploads/classroomvirtual/reports/zoom/',
                    'name' => 'setting.zoom_urlreports.name',
                    'description' => 'setting.zoom_urlreports.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.plugNmeetUrlReports',
                    'value' => 'uploads/classroomvirtual/reports/plugNmeet/',
                    'name' => 'setting.plugnmeet_urlreports.name',
                    'description' => 'setting.plugnmeet_urlreports.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clickMeetingUrlReports',
                    'value' => 'uploads/classroomvirtual/reports/clickMeeting/',
                    'name' => 'setting.clickmeeting_urlreports.name',
                    'description' => 'setting.clickmeeting_urlreports.description',
                    'sort' => 12,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'id' => 10,
            'group' => 'Libraries',
            'code' => SettingGroupCode::LIBRARIES,
            'position' => 10,
            'settings' => [
                [
                    'code' => 'app.library.enabled',
                    'value' => 'true',
                    'name' => 'setting.library_enabled.name',
                    'description' => 'setting.library_enabled.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.library.audio.local',
                    'value' => 'true',
                    'name' => 'setting.library_audio_local.name',
                    'description' => 'setting.library_audio_local.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.library.audio.path',
                    'value' => '/uploads/library/audio',
                    'name' => 'setting.library_audio_path.name',
                    'description' => 'setting.library_audio_path.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.library.file.path',
                    'value' => '/uploads/library/files',
                    'name' => 'setting.library_file_path.name',
                    'description' => 'setting.library_file_path.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.library.data.page_size',
                    'value' => 10,
                    'name' => 'setting.library_data_page_size.name',
                    'description' => 'setting.library_data_page_size.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.library.comments.default_is_visible',
                    'value' => 'false',
                    'name' => 'setting.library_comments.name',
                    'description' => 'setting.library_comments.description',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
        [
            'id' => 11,
            'group' => 'Challenges',
            'code' => SettingGroupCode::CHALLENGES,
            'position' => 11,
            'settings' => [
                [
                    'code' => 'app.challengeloops',
                    'value' => 3,
                    'name' => 'setting.challenge_loops.name',
                    'description' => 'setting.challenge_loops.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.pointsforwin',
                    'value' => 50,
                    'name' => 'setting.points_for_win.name',
                    'description' => 'setting.points_for_win.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.pointsforloose',
                    'value' => -25,
                    'name' => 'setting.points_for_lose.name',
                    'description' => 'setting.points_for_lose.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.pointsfortie',
                    'value' => 25,
                    'name' => 'setting.points_fortie.name',
                    'description' => 'setting.points_fortie.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.pointsfortiewithoutcorrects',
                    'value' => -25,
                    'name' => 'setting.points_corrects.name',
                    'description' => 'setting.points_corrects.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.pointsforleft',
                    'value' => -50,
                    'name' => 'setting.points_for_left.name',
                    'description' => 'setting.points_for_left.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.totalDuels',
                    'value' => 50,
                    'name' => 'setting.total_duels.name',
                    'description' => 'setting.total_duels.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'app.secondsPerQuestion',
                    'value' => 20,
                    'name' => 'setting.seconds_per_question.name',
                    'description' => 'setting.seconds_per_question.description',
                    'sort' => 15,
                    'options' => [],
                    'type' => 'integer',
                ],
            ],
        ],
        [
            'id' => 12,
            'group' => 'User',
            'code' => SettingGroupCode::USER,
            'position' => 12,
            'settings' => [
                [
                    'code' => 'app.user.dni',
                    'value' => 'false',
                    'name' => 'setting.user_dni.name',
                    'description' => 'setting.user_dni.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.user.editCode',
                    'value' => 'false',
                    'name' => 'setting.edit_code.name',
                    'description' => 'setting.edit_code.description',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.user.extra_fields',
                    'value' => '',
                    'name' => 'setting.user.extra_fields.name',
                    'description' => 'setting.user.extra_fields.description',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'text',
                ],
            ],
        ],
        [
            'id' => 13,
            'group' => 'Stats',
            'code' => SettingGroupCode::STATS,
            'position' => 13,
            'settings' => [
                [
                    'code' => 'app.stats.accumulative',
                    'value' => 'false',
                    'name' => 'setting.stats_acumulative.name',
                    'description' => 'setting.stats_acumulative.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                // ##                 STATS FORMATION GROUP                 ###
                [
                    'code' => 'app.stats.lite_formation',
                    'value' => '[]',
                    'name' => 'setting.lite_formation.name',
                    'description' => 'setting.lite_formation.description',
                    'sort' => 100,
                    'options' => [
                        'app.stats.lite_formation.formationHours',
                        'app.stats.lite_formation.peopleWithCourses',
                        'app.stats.lite_formation.courseStartedAndFinished',
                        'app.stats.lite_formation.requiredCourses',
                        'app.stats.lite_formation.general',
                        'app.stats.lite_formation.openedCourses',
                        'app.stats.lite_formation.educativeStatus',
                        'app.stats.lite_formation.gamifiedPills',
                        'app.stats.lite_formation.gamifiedTest',
                        'app.stats.lite_formation.peoplePerformance',
                        'app.stats.lite_formation.coursesByStars',
                        'app.stats.lite_formation.structureAndHotel',
                        'app.stats.lite_formation.schoolFinishedAndProgress',
                        'app.stats.lite_formation.coursesBySchool',
                        'app.stats.lite_formation.coursesByDepartment',
                        'app.stats.lite_formation.usersMoreActivesByCourses',
                    ],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.stats.lite_formation.formationHours',
                    'value' => 'true',
                    'name' => 'setting.lite_formation.formationHours.name',
                    'description' => 'setting.lite_formation.formationHours.description',
                    'sort' => 101,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.peopleWithCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.peopleWithCourses.name',
                    'description' => 'setting.lite_formation.peopleWithCourses.description',
                    'sort' => 102,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.courseStartedAndFinished',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.courseStartedAndFinished.name',
                    'description' => 'setting.lite_formation.courseStartedAndFinished.description',
                    'sort' => 103,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.requiredCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.requiredCourses.name',
                    'description' => 'setting.lite_formation.requiredCourses.description',
                    'sort' => 104,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.general',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.general.name',
                    'description' => 'setting.lite_formation.general.description',
                    'sort' => 105,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.openedCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.openedCourses.name',
                    'description' => 'setting.lite_formation.openedCourses.description',
                    'sort' => 106,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.educativeStatus',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.educativeStatus.name',
                    'description' => 'setting.lite_formation.educativeStatus.description',
                    'sort' => 107,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.gamifiedPills',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.gamifiedPills.name',
                    'description' => 'setting.lite_formation.gamifiedPills.description',
                    'sort' => 108,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.gamifiedTest',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.gamifiedTest.name',
                    'description' => 'setting.lite_formation.gamifiedTest.description',
                    'sort' => 109,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.peoplePerformance',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.peoplePerformance.name',
                    'description' => 'setting.lite_formation.peoplePerformance.description',
                    'sort' => 110,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.coursesByStars',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.coursesByStars.name',
                    'description' => 'setting.lite_formation.coursesByStars.description',
                    'sort' => 111,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.structureAndHotel',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.structureAndHotel.name',
                    'description' => 'setting.lite_formation.structureAndHotel.description',
                    'sort' => 112,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.schoolFinishedAndProgress',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.schoolFinishedAndProgress.name',
                    'description' => 'setting.lite_formation.schoolFinishedAndProgress.description',
                    'sort' => 113,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.coursesBySchool',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.coursesBySchool.name',
                    'description' => 'setting.lite_formation.coursesBySchool.description',
                    'sort' => 114,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.coursesByDepartment',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.coursesByDepartment.name',
                    'description' => 'setting.lite_formation.coursesByDepartment.description',
                    'sort' => 115,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_formation.usersMoreActivesByCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_formation.usersMoreActivesByCourses.name',
                    'description' => 'setting.lite_formation.usersMoreActivesByCourses.description',
                    'sort' => 116,
                    'options' => [],
                    'type' => 'bool',
                ],
                // ##                 STATS EVOLUTION GROUP                 ###
                [
                    'code' => 'app.stats.lite_evolution',
                    'value' => '[]',
                    'name' => 'setting.lite_evolution.name',
                    'description' => 'setting.lite_evolution.description',
                    'sort' => 200,
                    'options' => [
                        'app.stats.lite_evolution.trainedPerson',
                        'app.stats.lite_evolution.startedCourses',
                        'app.stats.lite_evolution.proccessCourses',
                        'app.stats.lite_evolution.finishedCourses',
                        'app.stats.lite_evolution.segmentedHours',
                        'app.stats.lite_evolution.userNewInPlatformThanFinishedOneCourse',
                    ],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.stats.lite_evolution.trainedPerson',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.trainedPerson.name',
                    'description' => 'setting.lite_evolution.trainedPerson.description',
                    'sort' => 201,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_evolution.startedCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.startedCourses.name',
                    'description' => 'setting.lite_evolution.startedCourses.description',
                    'sort' => 202,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_evolution.proccessCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.proccessCourses.name',
                    'description' => 'setting.lite_evolution.proccessCourses.description',
                    'sort' => 203,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_evolution.finishedCourses',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.finishedCourses.name',
                    'description' => 'setting.lite_evolution.finishedCourses.description',
                    'sort' => 204,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_evolution.segmentedHours',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.segmentedHours.name',
                    'description' => 'setting.lite_evolution.segmentedHours.description',
                    'sort' => 205,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_evolution.userNewInPlatformThanFinishedOneCourse',
                    'value' => 'false',
                    'name' => 'setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name',
                    'description' => 'setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description',
                    'sort' => 206,
                    'options' => [],
                    'type' => 'bool',
                ],

                // ##                 STATS DEMOGRAPHY GROUP                 ###
                [
                    'code' => 'app.stats.lite_demography',
                    'value' => '[]',
                    'name' => 'setting.lite_demography.name',
                    'description' => 'setting.lite_demography.description',
                    'sort' => 300,
                    'options' => [
                        'app.stats.lite_demography.usersBySexAndAge',
                        'app.stats.lite_demography.ageDistribution',
                        'app.stats.lite_demography.deviceDistribution',
                        'app.stats.lite_demography.usersByCountries',
                    ],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.stats.lite_demography.usersBySexAndAge',
                    'value' => 'false',
                    'name' => 'setting.lite_demography.usersBySexAndAge.name',
                    'description' => 'setting.lite_demography.usersBySexAndAge.description',
                    'sort' => 301,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_demography.ageDistribution',
                    'value' => 'false',
                    'name' => 'setting.lite_demography.ageDistribution.name',
                    'description' => 'setting.lite_demography.ageDistribution.description',
                    'sort' => 302,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_demography.deviceDistribution',
                    'value' => 'false',
                    'name' => 'setting.lite_demography.deviceDistribution.name',
                    'description' => 'setting.lite_demography.deviceDistribution.description',
                    'sort' => 303,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_demography.usersByCountries',
                    'value' => 'false',
                    'name' => 'setting.lite_demography.usersByCountries.name',
                    'description' => 'setting.lite_demography.usersByCountries.description',
                    'sort' => 304,
                    'options' => [],
                    'type' => 'bool',
                ],

                // ##                 STATS ACTIVITY GROUP                 ###
                [
                    'code' => 'app.stats.lite_activity',
                    'value' => '[]',
                    'name' => 'setting.lite_activity.name',
                    'description' => 'setting.lite_activity.description',
                    'sort' => 400,
                    'options' => [
                        'app.stats.lite_activity.activityInfo',
                        'app.stats.lite_activity.accessDays',
                        'app.stats.lite_activity.platformAccessByHours',
                        'app.stats.lite_activity.courseStartTime',
                        'app.stats.lite_activity.courseEndTime',
                        'app.stats.lite_activity.coursesStartedVsFinished',
                        'app.stats.lite_activity.usersMoreActivesByActivity',
                    ],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.stats.lite_activity.activityInfo',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.activityInfo.name',
                    'description' => 'setting.lite_activity.activityInfo.description',
                    'sort' => 401,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.accessDays',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.accessDays.name',
                    'description' => 'setting.lite_activity.accessDays.description',
                    'sort' => 402,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.platformAccessByHours',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.platformAccessByHours.name',
                    'description' => 'setting.lite_activity.platformAccessByHours.description',
                    'sort' => 403,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.courseStartTime',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.courseStartTime.name',
                    'description' => 'setting.lite_activity.courseStartTime.description',
                    'sort' => 404,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.courseEndTime',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.courseEndTime.name',
                    'description' => 'setting.lite_activity.courseEndTime.description',
                    'sort' => 405,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.coursesStartedVsFinished',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.coursesStartedVsFinished.name',
                    'description' => 'setting.lite_activity.coursesStartedVsFinished.description',
                    'sort' => 406,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_activity.usersMoreActivesByActivity',
                    'value' => 'false',
                    'name' => 'setting.lite_activity.usersMoreActivesByActivity.name',
                    'description' => 'setting.lite_activity.usersMoreActivesByActivity.description',
                    'sort' => 407,
                    'options' => [],
                    'type' => 'bool',
                ],

                // ##                 STATS ITINERARY GROUP                 ###
                [
                    'code' => 'app.stats.lite_itinerary',
                    'value' => '[]',
                    'name' => 'setting.lite_itinerary.name',
                    'description' => 'setting.lite_itinerary.description',
                    'sort' => 500,
                    'options' => [
                        'app.stats.lite_itinerary.itinerariesStartedAndFinished',
                        'app.stats.lite_itinerary.itinerariesCompletedByCountries',
                    ],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.stats.lite_itinerary.itinerariesStartedAndFinished',
                    'value' => 'false',
                    'name' => 'setting.lite_itinerary.itinerariesStartedAndFinished.name',
                    'description' => 'setting.lite_itinerary.itinerariesStartedAndFinished.description',
                    'sort' => 501,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.stats.lite_itinerary.itinerariesCompletedByCountries',
                    'value' => 'false',
                    'name' => 'setting.lite_itinerary.itinerariesCompletedByCountries.name',
                    'description' => 'setting.lite_itinerary.itinerariesCompletedByCountries.description',
                    'sort' => 502,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
        [
            'id' => 14,
            'group' => 'Api V1',
            'code' => SettingGroupCode::APIV1,
            'position' => 14,
            'settings' => [
                [
                    'code' => 'api.date_range_max',
                    'value' => '7',
                    'name' => 'setting.maximo_fechas.name',
                    'description' => 'setting.maximo_fechas.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'api.max_requests_per_hour',
                    'value' => '6',
                    'name' => 'setting.maximo_horas.name',
                    'description' => 'setting.maximo_horas.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'integer',
                ],
                [
                    'code' => 'api.max_requests_per_day',
                    'value' => '100',
                    'name' => 'setting.maximo_dia.name',
                    'description' => 'setting.maximo_dia.description',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'integer',
                ],
            ],
        ],
        [
            'id' => 16,
            'group' => 'HELP',
            'code' => SettingGroupCode::HELP,
            'position' => 16,
            'settings' => [
                [
                    'code' => 'app.help.user.pdf',
                    'value' => 'false',
                    'name' => 'setting.help.user.name',
                    'description' => 'setting.help.user.description',
                    'type' => 'bool',
                    'sort' => 31,
                    'options' => [],
                ],
            ],
        ],

        [
            'id' => 17,
            'group' => 'SURVEY',
            'code' => SettingGroupCode::SURVEY,
            'position' => 17,
            'settings' => [
                [
                    'code' => 'app.survey.hide.empty_comment',
                    'value' => 'true',
                    'name' => 'setting.survey.hide_empty_comment.name',
                    'description' => 'setting.survey.hide_empty_comment.description',
                    'type' => 'bool',
                    'sort' => 1,
                    'options' => [],
                ],
                [
                    'code' => 'app.survey.show_only_ratings',
                    'value' => 'false',
                    'name' => 'setting.survey.show_only_ratings.name',
                    'description' => 'setting.survey.show_only_ratings.description',
                    'type' => 'bool',
                    'sort' => 2,
                    'options' => [],
                ],
                [
                    'code' => 'app.survey.post_nps.enabled',
                    'value' => 'true',
                    'name' => 'app.survey.post_nps.enabled.name',
                    'description' => 'app.survey.post_nps.enabled.description',
                    'type' => 'bool',
                    'sort' => 3,
                    'options' => [],
                ],
            ],
        ],

        [
            'id' => 18,
            'group' => 'SAML',
            'code' => SettingGroupCode::SAML,
            'position' => 18,
            'settings' => [
                [
                    'code' => 'saml.enabled',
                    'value' => 'false',
                    'name' => 'setting.saml.name',
                    'description' => 'setting.saml.description',
                    'type' => 'bool',
                    'sort' => 1,
                    'options' => [],
                ],

                [
                    'code' => 'saml.destination',
                    'value' => 'https://url-saml-examplñe',
                    'name' => 'setting.saml_destination.name',
                    'description' => 'setting.saml_destination.description',
                    'type' => 'text',
                    'sort' => 1,
                    'options' => [],
                ],

                [
                    'code' => 'saml.issuer',
                    'value' => '169f624b-2663-4eb5',
                    'name' => 'setting.saml_issuer.name',
                    'description' => 'setting.saml_issuer.description',
                    'type' => 'text',
                    'sort' => 1,
                    'options' => [],
                ],
                [
                    'code' => 'saml.logout_url',
                    'value' => 'https://url-saml-examplñe',
                    'name' => 'setting.saml_logout.name',
                    'description' => 'setting.saml_logout.description',
                    'type' => 'text',
                    'sort' => 1,
                    'options' => [],
                ],

                [
                    'code' => 'saml.user.required_attributes',
                    'value' => '{"email":"emailaddress"}',
                    'name' => 'setting.saml_user_required.name',
                    'description' => 'setting.saml_user_required.description',
                    'type' => 'json',
                    'sort' => 1,
                    'options' => [],
                ],
                [
                    'code' => 'saml.user.filters',
                    'value' => '{"jobtitle":[1],"physicaldeliveryofficename":[2],"department":[3]}',
                    'name' => 'setting.saml_user_filters.name',
                    'description' => 'setting.saml_user_filters.description',
                    'type' => 'json',
                    'sort' => 1,
                    'options' => [],
                ],
            ],
        ],
    ];
}
