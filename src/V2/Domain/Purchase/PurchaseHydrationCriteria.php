<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class PurchaseHydrationCriteria extends HydrationCriteria
{
    private bool $withPurchaseItem = false;
    private bool $withPurchasableItem = false;
    private bool $withUser = false;

    public function isEmpty(): bool
    {
        return false === $this->withPurchaseItem
            && false === $this->withUser;
    }

    public function withPurchaseItem(bool $withPurchasableItem = false): self
    {
        $this->withPurchaseItem = true;
        $this->withPurchasableItem = $withPurchasableItem;

        return $this;
    }

    public function needsPurchaseItem(): bool
    {
        return $this->withPurchaseItem;
    }

    public function needsPurchasableItem(): bool
    {
        return $this->withPurchasableItem;
    }

    public function withUser(): self
    {
        $this->withUser = true;

        return $this;
    }

    public function needsUser(): bool
    {
        return $this->withUser;
    }
}
