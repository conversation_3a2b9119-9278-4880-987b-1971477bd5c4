<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\Entity\User;
use App\V2\Domain\Shared\Entity\LifeCycleEntity;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends LifeCycleEntity<Uuid>
 */
class Purchase extends LifeCycleEntity
{
    private ?PurchaseItemCollection $purchaseItems = null;
    private ?User $user = null;

    public function __construct(
        Uuid $id,
        private readonly Id $userId,
        private readonly PurchaseStatus $status,
        private readonly Money $amount,
        private readonly TaxRate $taxRate,
        private readonly Money $taxAmount,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ) {
        parent::__construct(
            id: $id,
            createdAt: $createdAt,
            updatedAt: $updatedAt,
            deletedAt: $deletedAt
        );
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getStatus(): PurchaseStatus
    {
        return $this->status;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function getTaxRate(): TaxRate
    {
        return $this->taxRate;
    }

    public function getTaxAmount(): Money
    {
        return $this->taxAmount;
    }

    public function getPurchaseItems(): ?PurchaseItemCollection
    {
        return $this->purchaseItems;
    }

    public function setPurchaseItems(PurchaseItemCollection $purchaseItems): self
    {
        $this->purchaseItems = $purchaseItems;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }
}
