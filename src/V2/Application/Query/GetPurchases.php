<?php

declare(strict_types=1);

namespace App\V2\Application\Query;

use App\V2\Domain\Bus\Query;
use App\V2\Domain\Purchase\PurchaseCriteria;

readonly class GetPurchases implements Query
{
    public function __construct(
        private PurchaseCriteria $criteria,
        private bool $withUser = false,
    ) {
    }

    public function getCriteria(): PurchaseCriteria
    {
        return $this->criteria;
    }

    public function withUser(): bool
    {
        return $this->withUser;
    }
}
