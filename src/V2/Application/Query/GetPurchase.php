<?php

declare(strict_types=1);

namespace App\V2\Application\Query;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class GetPurchase implements Query
{
    public function __construct(
        private Uuid $purchaseId,
        private ?User $purchaseOwner = null,
        private bool $withItems = false,
        private bool $withUser = false,
    ) {
    }

    public function getPurchaseId(): Uuid
    {
        return $this->purchaseId;
    }

    public function getPurchaseOwner(): ?User
    {
        return $this->purchaseOwner;
    }

    public function withItems(): bool
    {
        return $this->withItems;
    }

    public function withUser(): bool
    {
        return $this->withUser;
    }
}
