<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Hydrator\Purchase\PurchaseHydratorCollection;
use App\V2\Application\Query\GetPurchases;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PaginatedPurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;

readonly class GetPurchasesQueryHandler
{
    public function __construct(
        private PurchaseRepository $purchaseRepository,
        private PurchaseHydratorCollection $purchaseHydratorCollection,
    ) {
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InfrastructureException
     * @throws HydratorException
     */
    public function handle(GetPurchases $query): PaginatedPurchaseCollection
    {
        $collection = $this->purchaseRepository->findBy($query->getCriteria());
        $totalItems = $this->purchaseRepository->countBy($query->getCriteria());

        if ($query->withUser()) {
            $hydrationCriteria = PurchaseHydrationCriteria::createEmpty()->withUser(true);

            $this->purchaseHydratorCollection->hydrate(
                $collection,
                $hydrationCriteria
            );
        }

        return new PaginatedPurchaseCollection(
            collection: $collection,
            totalItems: $totalItems
        );
    }
}
