<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\GetPurchases;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Purchase\PurchaseCriteriaTransformer;
use App\V2\Infrastructure\Purchase\PurchaseTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Admin\GetPurchasesValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetPurchasesController extends QueryBusAccessor
{
    /**
     * @throws RequestAttributeExtractorException
     * @throws CollectionException
     * @throws InvalidCurrencyCodeException
     * @throws ValidatorException
     * @throws InvalidUuidException
     */
    public function __invoke(Request $request): Response
    {
        $queryParameters = $request->query->all();
        GetPurchasesValidator::validateGetPurchasesRequest($queryParameters);

        $criteria = PurchaseCriteriaTransformer::fromArray($queryParameters);

        $purchases = $this->ask(
            new GetPurchases(
                criteria: $criteria,
                withUser: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                PurchaseTransformer::fromPurchaseCollectionToArray($purchases->getCollection())
            )
                ->addPaginationMetadata($purchases, $criteria)
                ->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
