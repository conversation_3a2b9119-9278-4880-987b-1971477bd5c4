<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase;

use App\V2\Domain\Purchase\Exception\PurchaseItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Domain\Purchase\PurchaseItemCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Financial\TaxRate;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use App\V2\Infrastructure\Shared\Purchase\PurchaseStatusTransformer;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALPurchaseRepository implements PurchaseRepository
{
    public function __construct(
        private Connection $connection,
        private string $purchaseTableName,
        private string $purchaseItemTableName,
    ) {
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws DBALException
     */
    #[\Override]
    public function put(Purchase $purchase): void
    {
        $this->connection->beginTransaction();

        try {
            $this->findOneBy(
                PurchaseCriteria::createById($purchase->getId())
            );

            $this->update($purchase);
        } catch (PurchaseNotFoundException) {
            $this->insert($purchase);
        }

        if ($purchase->getPurchaseItems()) {
            foreach ($purchase->getPurchaseItems() as $purchaseItem) {
                $this->putPurchaseItem($purchaseItem);
            }
        }

        $this->connection->commit();
    }

    /**
     * @throws PurchaseRepositoryException
     */
    private function insert(Purchase $purchase): void
    {
        try {
            $this->connection->insert(
                table: $this->purchaseTableName,
                data: $this->fromPurchaseToArray($purchase),
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     */
    private function update(Purchase $purchase): void
    {
        try {
            $data = $this->fromPurchaseToArray($purchase);
            unset($data['id']);

            $this->connection->update(
                table: $this->purchaseTableName,
                data: $data,
                criteria: ['id' => $purchase->getId()->value()],
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findOneBy(PurchaseCriteria $criteria): Purchase
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new PurchaseNotFoundException();
            }

            return $this->fromArrayToPurchase($result);
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws CollectionException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findBy(PurchaseCriteria $criteria): PurchaseCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new PurchaseCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToPurchase($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     */
    #[\Override]
    public function countBy(PurchaseCriteria $criteria): int
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->select('COUNT(*) as count')
                ->executeQuery()
                ->fetchAssociative();

            return (int) $result['count'];
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     */
    #[\Override]
    public function delete(Purchase $purchase): void
    {
        try {
            $purchase->markAsDeleted();

            $this->update($purchase);
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    private function getQueryBuilderByCriteria(PurchaseCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->purchaseTableName, 't')
            ->andWhere('deleted_at IS NULL');

        if ($criteria->getUserId()) {
            $qb->andWhere('user_id = :user_id')
                ->setParameter('user_id', $criteria->getUserId()->value());
        }

        if ($criteria->getStatus()) {
            $qb->andWhere('status = :status')
                ->setParameter('status', PurchaseStatusTransformer::toString($criteria->getStatus()));
        }

        if ($criteria->getMinAmount()) {
            $qb->andWhere('amount >= :min_amount')
                ->setParameter('min_amount', $criteria->getMinAmount()->value());
        }

        if ($criteria->getMaxAmount()) {
            $qb->andWhere('amount <= :max_amount')
                ->setParameter('max_amount', $criteria->getMaxAmount()->value());
        }

        if ($criteria->getStartDate()) {
            $qb->andWhere('created_at >= :start_date')
                ->setParameter('start_date', $criteria->getStartDate()->format('Y-m-d H:i:s'));
        }

        if ($criteria->getEndDate()) {
            $qb->andWhere('created_at <= :end_date')
                ->setParameter('end_date', $criteria->getEndDate()->format('Y-m-d H:i:s'));
        }

        if ($criteria->getPurchasableId()) {
            $qb->join(
                't',
                $this->purchaseItemTableName,
                'pi',
                'pi.purchase_id = t.id'
            )
                ->andWhere('pi.purchasable_item_id = :purchasable_id')
                ->setParameter('purchasable_id', $criteria->getPurchasableId()->value());
        }

        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb, true);

        return $qb;
    }

    private function fromPurchaseToArray(Purchase $purchase): array
    {
        return [
            'id' => $purchase->getId()->value(),
            'user_id' => $purchase->getUserId()->value(),
            'status' => PurchaseStatusTransformer::toString($purchase->getStatus()),
            'amount' => $purchase->getAmount()->value(),
            'currency_code' => CurrencyCodeTransformer::toString($purchase->getAmount()->currency()->code()),
            'tax_rate' => $purchase->getTaxRate()->value(),
            'tax_amount' => $purchase->getTaxAmount()->value(),
            'created_at' => DBALDateTimeFormatter::format($purchase->getCreatedAt()),
            'updated_at' => DBALDateTimeFormatter::format($purchase->getUpdatedAt()),
            'deleted_at' => DBALDateTimeFormatter::format($purchase->getDeletedAt()),
        ];
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    private function fromArrayToPurchase(array $values): Purchase
    {
        return new Purchase(
            id: new Uuid($values['id']),
            userId: new Id((int) $values['user_id']),
            status: PurchaseStatusTransformer::fromString($values['status']),
            amount: Money::create(
                amount: (int) $values['amount'],
                currency: new Currency(CurrencyCodeTransformer::fromString($values['currency_code']))
            ),
            taxRate: new TaxRate((float) $values['tax_rate']),
            taxAmount: Money::create(
                amount: (int) $values['tax_amount'],
                currency: new Currency(CurrencyCodeTransformer::fromString($values['currency_code']))
            ),
            createdAt: DBALDateTimeFormatter::parse($values['created_at']),
            updatedAt: DBALDateTimeFormatter::parse($values['updated_at']),
            deletedAt: DBALDateTimeFormatter::parse($values['deleted_at']),
        );
    }

    /**
     * @throws CriteriaException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function putPurchaseItem(PurchaseItem $purchaseItem): void
    {
        try {
            $this->findOnePurchaseItemBy(
                PurchaseItemCriteria::createById($purchaseItem->getId())
            );

            $this->updatePurchaseItem($purchaseItem);
        } catch (PurchaseItemNotFoundException) {
            $this->insertPurchaseItem($purchaseItem);
        }
    }

    private function insertPurchaseItem(PurchaseItem $purchaseItem): void
    {
        try {
            $this->connection->insert(
                table: $this->purchaseItemTableName,
                data: $this->fromPurchaseItemToArray($purchaseItem),
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    private function updatePurchaseItem(PurchaseItem $purchaseItem): void
    {
        try {
            $data = $this->fromPurchaseItemToArray($purchaseItem);
            unset($data['id']);

            $this->connection->update(
                table: $this->purchaseItemTableName,
                data: $data,
                criteria: ['id' => $purchaseItem->getId()->value()],
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws PurchaseRepositoryException
     * @throws PurchaseItemNotFoundException
     * @throws InvalidUuidException
     */
    public function findOnePurchaseItemBy(PurchaseItemCriteria $criteria): ?PurchaseItem
    {
        try {
            $result = $this->getPurchaseItemQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new PurchaseItemNotFoundException();
            }

            return $this->fromArrayToPurchaseItem($result);
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     * @throws CollectionException
     */
    public function findPurchaseItemsBy(PurchaseItemCriteria $criteria): PurchaseItemCollection
    {
        try {
            $result = $this->getPurchaseItemQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new PurchaseItemCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToPurchaseItem($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    public function countPurchaseItemsBy(PurchaseItemCriteria $criteria): int
    {
        try {
            $result = $this->getPurchaseItemQueryBuilderByCriteria($criteria)
                ->select('COUNT(*) as count')
                ->executeQuery()
                ->fetchAssociative();

            return (int) $result['count'];
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    public function deletePurchaseItem(PurchaseItem $purchaseItem): void
    {
        try {
            $this->connection->delete(
                table: $this->purchaseItemTableName,
                criteria: ['id' => $purchaseItem->getId()->value()],
            );
        } catch (DBALException $e) {
            throw PurchaseRepositoryException::fromPrevious($e);
        }
    }

    private function getPurchaseItemQueryBuilderByCriteria(PurchaseItemCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->purchaseItemTableName, 't');

        if (null !== $criteria->getPurchaseId()) {
            $qb->andWhere('purchase_id = :purchase_id')
                ->setParameter('purchase_id', $criteria->getPurchaseId()->value());
        }

        if (null !== $criteria->getPurchaseIds()) {
            $qb->andWhere('purchase_id IN (:purchase_ids)')
                ->setParameter('purchase_ids', $criteria->getPurchaseIds()->all(), ArrayParameterType::STRING);
        }

        if (null !== $criteria->getPurchasableItemId()) {
            $qb->andWhere('purchasable_item_id = :purchasable_item_id')
                ->setParameter('purchasable_item_id', $criteria->getPurchasableItemId()->value());
        }

        if (null !== $criteria->getMinPrice()) {
            $qb->andWhere('price_amount >= :min_price')
                ->setParameter('min_price', $criteria->getMinPrice()->value());
        }

        if (null !== $criteria->getMaxPrice()) {
            $qb->andWhere('price_amount <= :max_price')
                ->setParameter('max_price', $criteria->getMaxPrice()->value());
        }

        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb, true);

        return $qb;
    }

    private function fromPurchaseItemToArray(PurchaseItem $purchaseItem): array
    {
        return [
            'id' => $purchaseItem->getId()->value(),
            'purchase_id' => $purchaseItem->getPurchaseId()->value(),
            'purchasable_item_id' => $purchaseItem->getPurchasableItemId()->value(),
            'price_amount' => $purchaseItem->getPrice()->value(),
            'price_currency' => CurrencyCodeTransformer::toString($purchaseItem->getPrice()->currency()->code()),
        ];
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    private function fromArrayToPurchaseItem(array $values): PurchaseItem
    {
        return new PurchaseItem(
            id: new Uuid($values['id']),
            purchaseId: new Uuid($values['purchase_id']),
            purchasableItemId: new Uuid($values['purchasable_item_id']),
            price: Money::create(
                amount: (int) $values['price_amount'],
                currency: new Currency(CurrencyCodeTransformer::fromString($values['price_currency']))
            ),
        );
    }
}
