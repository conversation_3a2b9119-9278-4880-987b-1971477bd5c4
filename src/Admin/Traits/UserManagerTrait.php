<?php

declare(strict_types=1);

namespace App\Admin\Traits;

use App\Entity\User;
use Doctrine\ORM\QueryBuilder;

trait UserManagerTrait
{
    public function getAssignedUsersByFilters(QueryBuilder $qb, ?User $manager): QueryBuilder
    {
        if (!$manager || !$manager->isManager()) {
            return $qb;
        }

        $managerFiltersByCategory = [];
        $managerFilters = $manager->getFilters();

        // Organizar los filtros del manager por categoría.
        foreach ($managerFilters as $filter) {
            $catId = $filter->getFilterCategory()->getId();
            $managerFiltersByCategory[$catId][] = $filter->getId();
        }

        if (!empty($managerFiltersByCategory)) {
            $orExpressions = [];
            foreach ($managerFiltersByCategory as $categoryId => $filterIds) {
                // Crear una expresión OR para cada categoría con los filtros de esa categoría.
                $alias = 'mf_' . $categoryId;
                $qb->join('u.filter', $alias);
                $orExpressions[] = $qb->expr()->in($alias . '.id', $filterIds);
            }

            // Combinar las expresiones OR de cada categoría con AND
            $andX = $qb->expr()->andX();
            foreach ($orExpressions as $orExpr) {
                $andX->add($orExpr);
            }

            $qb->andWhere($andX);
        }

        return $qb;
    }

    public function getManagerCoursesFilters(QueryBuilder $qb, User $manager): QueryBuilder
    {
        $managerCourses = $manager->getManagedCourses()->toArray();

        if (!empty($managerCourses)) {
            $qb->andWhere($qb->expr()->in('c.id', ':managerCourses'))
                ->setParameter('managerCourses', $managerCourses);
        }

        return $qb;
    }

    private function setManagerFilters(User $user, QueryBuilder $query): QueryBuilder
    {
        if (!$user->isAdmin() && $user->isManager()) {
            $query = $this->getAssignedUsersByFilters($query, $user);
            $query = $this->getManagerCoursesFilters($query, $user);
        }

        return $query;
    }

    public function ensureManagerCanManageFilters(User $user, array $filters): bool
    {
        $managerFilters = $user->getFilters()->toArray();
        $searchFilters = [];
        foreach ($filters as $categoryName => $categoryFilters) {
            foreach ($categoryFilters as $filter) {
                $searchFilters[] = $filter['id'];
            }
        }

        $filtersMatch = array_intersect($searchFilters, array_map(fn ($filter) => $filter->getId(), $managerFilters));

        if (empty($filtersMatch)) {
            return false;
        }

        return true;
    }
}
