import axios from "axios";

/**
 *
 * @param id
 * @param course
 * @param code
 * @param startAt
 * @param finishAt
 * @param totalHours
 * @param usersPerGroup
 * @param didacticGuide
 * @param objectiveAndContent
 * @param approvedCriteriaValues
 * @param configAnnouncement
 * @param chapterTiming
 * @param timezone
 * @param extra
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementGeneralInfo(
    {
        id, course, code, startAt, finishAt, totalHours, usersPerGroup, didacticGuide, objectiveAndContent,
        approvedCriteriaValues, configAnnouncement, chapterTiming, timezone, extra
    }
) {

    const headers = {
        'Content-Type': 'multipart/form-data'
    };

    return axios.post(
        '/admin/announcement/form/general-info',
        {
            id,
            courseId: course.id,
            code,
            startAt,
            finishAt,
            totalHours,
            usersPerGroup,
            didacticGuide,
            objectiveAndContent,
            approvedCriteriaValues,
            configAnnouncement,
            chapterTiming,
            timezone,
            extra: extra.reduce((acc, item) => {
                acc[item.id] = item.value || null;
                return acc;
            }, {})
        },
        { headers }
    ).then(r => {
        return r.data;
    }).catch(e => ({error: true, data: 'Failed to make request', trace: e}));
}

/**
 *
 * @param id
 * @param actionType
 * @param actionCode
 * @param denomination
 * @param contactPerson
 * @param contactPersonEmail
 * @param contactPersonTelephone
 * @param usersPerGroup
 * @param configAnnouncement
 * @param approvedCriteriaValues
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementBonus(
    {
        id,
        actionType, actionCode,
        denomination, contactPerson,
        contactPersonEmail, contactPersonTelephone,
        usersPerGroup, configAnnouncement, approvedCriteriaValues
    }
) {
    const headers = {
        'Content-Type': 'multipart/form-data'
    };

    return axios.post(
        'admin/announcement/form/bonification',
        {
            id, actionType, actionCode, denomination, contactPerson,
            contactPersonEmail, contactPersonTelephone, usersPerGroup,
            configAnnouncement, approvedCriteriaValues
        },
        {headers})
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

/**
 *
 * @param id
 * @param students
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementStudents({ id, students }) {
    const headers = {
        'Content-Type': 'multipart/form-data'
    };
    return axios.post('admin/announcement/form/students', { id, students }, { headers })
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

/**
 *
 * @param id
 * @param students
 * @param allowSetURL
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementGroups({ id, students }) {
    const groupData = {
        id,
        data: []
    };
    students.forEach(group => {
        const { id, code, denomination, companyProfile, companyCif, fileNumber, numberOfSessions, place, sessions, typeMoney, cost } = group;
        groupData.data.push({
            id,
            code,
            denomination,
            companyProfile,
            companyCif,
            fileNumber,
            numberOfSessions,
            place,
            sessions: sessions.map((session) => ({
                ...session,
                ...(!session.type || session.type === 'VIRTUAL' ? {
                    virtual_meeting_type: 'fixed',
                    virtual_meeting_url: session.url
                }: {})
            })),
            typeMoney,
            cost
        });
    })

    return axios.post('/admin/announcement/form/group-info', groupData)
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

/**
 *
 * @param id
 * @param configAnnouncement
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementCommunication({ id, configAnnouncement }) {
    return axios.post('/admin/announcement/form/communication', { id, configAnnouncement })
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

/**
 *
 * @param id
 * @param configAnnouncement
 * @param typeSurvey
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementSurvey({ id, configAnnouncement, typeSurvey }) {
    return axios.post('/admin/announcement/form/survey', { id, configAnnouncement, typeSurvey })
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

export function saveAnnouncementCertificate({ id, isConfirmationRequiredDiploma, typeDiploma, configAnnouncement }) {
    return axios.post('/admin/announcement/form/certificate', { id, isConfirmationRequiredDiploma, typeDiploma, configAnnouncement })
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

/**
 * @param id
 * @param configAnnouncement
 * @param alertTypeTutorValues
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveAnnouncementAlerts({id, configAnnouncement, alertTypeTutorValues}) {
    return axios.post(
        '/admin/announcement/form/alerts',
        {
            id, configAnnouncement, alertTypeTutorValues
        })
        .then(r => (r.data))
        .catch(e => ({error: true, data: 'Failed to make request', trace: e}));
}

/**
 * Save an announcement tutor from selected user
 * @param formData
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveGroupTutorInfo(formData) {
    const headers = {
        'Content-Type': 'multipart/form-data'
    };
    return axios.post('/admin/announcement/form/group-tutor', formData, { headers })
        .then(r => (r.data))
        .catch(e => ({error: true, data: 'Failed to make request', trace: e}));
}

/**
 * Save an announcement tutor by creating User
 * @param formData
 * @return {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function saveNewGroupTutor(formData) {
    const headers = {
        'Content-Type': 'multipart/form-data'
    };
    return axios.post('/admin/announcement/form/new-announcement-tutor', formData, { headers })
        .then(r => (r.data))
        .catch(e => ({error: true, data: 'Failed to make request', trace: e}));
}

export function saveAnnouncementGeneralInfoExtern({ id = -1, code, type, course, students, timezone, startAt, finishAt, totalHours, extra }) {

    return axios.post('/admin/announcement/form/save/general-info-extern', {
        id,
        code,
        type,
        course,
        students,
        timezone,
        startAt,
        finishAt,
        totalHours,
        extra: extra.reduce((acc, item) => {
            acc[item.id] = item.value || null;
            return acc;
        }, {})
     })
        .then(r => (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e }));
}
