import axios from "axios";
import {validEmail} from "../../../../common/utils/utils";
import {
    COURSE_TYPE_ONLINE,
    COURSE_TYPE_MIXED,
    COURSE_TYPE_ON_SITE,
    COURSE_TYPE_VIRTUAL_CLASSROOM,
    COURSE_TYPE_MIXED_EXTERN,
    COURSE_TYPE_ON_SITE_EXTERN,
    COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN,
} from "../../../../course/mixins/constants";

/**
 * Constants
 */

/**
 * **************************************************************************
 * ***  With these declaration, the components/methods/store may change   ***
 * **************************************************************************
 */
export const SOURCE_ANNOUNCEMENT_INTERN = 'INTERN';
export const SOURCE_ANNOUNCEMENT_EXTERN = 'EXTERN';
export const ANNOUNCEMENT_SOURCE = {
    INTERN: SOURCE_ANNOUNCEMENT_INTERN,
    EXTERN: SOURCE_ANNOUNCEMENT_EXTERN
};

export const ANNOUNCEMENT_STATUS_CONFIGURATION = 'CONFIGURATION';
export const ANNOUNCEMENT_STATUS_ACTIVE = 'ACTIVE';
export const ANNOUNCEMENT_STATUS_IN_PROGRESS = 'IN_PROGRESS';
export const ANNOUNCEMENT_STATUS_FINISHED = 'FINISHED';
export const ANNOUNCEMENT_STATUS_INACTIVE = 'INACTIVE';
export const ANNOUNCEMENT_STATUS_ARCHIVED = 'ARCHIVED';

export const ANNOUNCEMENT_STATUS = {
    CONFIGURATION   :   ANNOUNCEMENT_STATUS_CONFIGURATION,
    ACTIVE          :   ANNOUNCEMENT_STATUS_ACTIVE,
    IN_PROGRESS     :   ANNOUNCEMENT_STATUS_IN_PROGRESS,
    FINISHED        :   ANNOUNCEMENT_STATUS_FINISHED,
    INACTIVE        :   ANNOUNCEMENT_STATUS_INACTIVE,
    ARCHIVED        :   ANNOUNCEMENT_STATUS_ARCHIVED,
};

/**
 * Main announcement default object
 */
export const ANNOUNCEMENT_DEFAULT_VALUES = {
    source: SOURCE_ANNOUNCEMENT_INTERN,

    id: null,
    status: ANNOUNCEMENT_STATUS_CONFIGURATION,
    notifiedAt: null,
    code: '',
    course: {
        typeCourseId: null
    },
    timezone: null,
    startAt: null,
    finishAt: null,
    extra: null,
    totalHours: 0,
    usersPerGroup: 0,// Value is used when
    didacticGuide: null,
    objectiveAndContent: '',
    chapterTiming: [],
    modalities: [],
    configAnnouncement: {},

    type: COURSE_TYPE_ONLINE,

    students: [],// based on context, it can be groups of students or a plain list of students

    subsidized: false,
    actionType: '',
    actionCode: '',
    denomination: '',
    contactPerson: '',
    contactPersonEmail: '',
    contactPersonTelephone: '',

    inspectorAccess: {
        user: '',
        password: '',
        url: '',
    },

    // enableCertificate: false,
    typeDiploma: null,
    isConfirmationRequiredDiploma: false,
    typeSurvey: null,

    approvedCriteriaValues: {},
    alertTypeTutorValues: {},
    typeMoney: null,
};

/**
 * Announcement validations
 */
export const ANNOUNCEMENT_VALIDATIONS = {
    AnnouncementCourse: {
        enabled: true,
        validate: (announcement) => {
            let error = {
                error: false,
                type: 'AnnouncementCourse',
            };

            const isValid = announcement.course !== null;
            if (isValid) return error;

            return {
                ...error,
                error: true,
                i18n: true,
                message: 'ANNOUNCEMENT.COURSE_REQUIRED',
                data: [],
            };
        },
    },
    AnnouncementGeneralInfo: {
        enabled: true,
        validate: (announcement) => {
            let error = {
                type: 'AnnouncementGeneralInfo',
            };

            const data = {};
            data['AnnouncementGeneralInfo-code'] = !announcement.code || announcement.code.length < 1;
            data['AnnouncementGeneralInfo-startAt'] =
                !announcement.startAt || announcement.startAt.length < 1;
            data['AnnouncementGeneralInfo-finishAt'] =
                !announcement.finishAt || announcement.finishAt.length < 1;
            if (announcement.subsidized) {
                data['AnnouncementGeneralInfo-didacticGuide'] = !announcement.didacticGuide;
            }
            data['AnnouncementGeneralInfo-totalHours'] = announcement.totalHours <= 0;
            data['AnnouncementGeneralInfo-usersPerGroup'] = announcement.usersPerGroup < 1;
            data['AnnouncementGeneralInfo-timezone'] = announcement.timezone == null;

            let isError = false;
            const keys = Object.keys(data);
            for (let i = 0; i < keys.length; i++) {
                if (data[keys[i]]) {
                    isError = true;
                    break;
                }
            }

            data.i18n = [];
            if (announcement.timezone == null || announcement.timezone.length < 1) {
                data.i18n.push('ANNOUNCEMENT.FORM.TIMEZONE.REQUIRED');
                isError = true;
            }

            return { ...error, error: isError, data };
        },
    },

    AnnouncementGeneralInfoExtern: {
        enabled: true,
        validate: (announcement) => {
            let error = {
                type: 'AnnouncementGeneralInfoExtern',
            };

            const data = {};
            data['AnnouncementGeneralInfoExtern-code'] = !announcement.code || announcement.code.length < 1;
            data['AnnouncementGeneralInfoExtern-startAt'] =
                !announcement.startAt || announcement.startAt.length < 1;
            data['AnnouncementGeneralInfoExtern-finishAt'] =
                !announcement.finishAt || announcement.finishAt.length < 1;
            data['AnnouncementGeneralInfoExtern-totalHours'] = announcement.totalHours <= 0;
            data['AnnouncementGeneralInfoExtern-timezone'] = announcement.timezone == null;
            // Course info
            data['AnnouncementGeneralInfoExtern-courseName'] = announcement.course?.name == null || announcement.course?.name?.length < 1;
            data['AnnouncementGeneralInfoExtern-courseType'] = announcement.type == null || announcement.type.length < 1;
            data['AnnouncementGeneralInfoExtern-courseCategory'] = announcement.course?.category == null || announcement.course?.category?.id == null;
            data['AnnouncementGeneralInfoExtern-courseLocale'] = announcement.course?.locale == null || announcement.course?.locale?.length < 1;

            const students = announcement.students;

            data['AnnouncementGeneralInfoExtern-place'] = students.length === 0 || students[0].place == null ||students[0].place.length < 1;
            data['AnnouncementGeneralInfoExtern-typeMoney'] = students.length === 0 || students[0].typeMoney == null;
            data['AnnouncementGeneralInfoExtern-tutorName'] = students.length === 0 || students[0].tutor?.name == null ||students[0].tutor?.name.length < 1;
            data['AnnouncementGeneralInfoExtern-tutorEmail'] = students.length === 0 || students[0].tutor?.email == null ||students[0].tutor?.email.length < 1;

            let isError = false;
            const keys = Object.keys(data);
            for (let i = 0; i < keys.length; i++) {
                if (data[keys[i]]) {
                    isError = true;
                    break;
                }
            }

            data.i18n = [];
            if (announcement.timezone == null || announcement.timezone.length < 1) {
                data.i18n.push('ANNOUNCEMENT.FORM.TIMEZONE.REQUIRED');
                isError = true;
            }

            return { ...error, error: isError, data };
        },
    },

    AnnouncementBonus: {
        enabled: true,
        validate: (announcement) => {
            let error = {
                type: 'AnnouncementBonus',
                error: false,
                data: {
                    'AnnouncementBonus-actionType': false,
                    'AnnouncementBonus-actionCode': false,
                    // 'AnnouncementBonus-denomination': false,
                    'AnnouncementBonus-contactPerson': false,
                    'AnnouncementBonus-contactPersonEmail': false,
                    'AnnouncementBonus-contactPersonTelephone': false,
                    'AnnouncementBonus-usersPerGroup': false,
                },
            };
            if (!announcement.subsidized) return error;

            const data = {};
            data.i18n = [];
            let isError = false;

            if (!announcement.actionType || announcement.actionType.length < 1) {
                isError = true;
                data['AnnouncementBonus-actionType'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.ENTITY.ACTION_TYPE_REQUIRED');
            }

            if (!announcement.actionCode || announcement.actionCode.length < 1) {
                isError = true;
                data['AnnouncementBonus-actionCode'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE_REQUIRED');
            }

            if (!announcement.contactPerson || announcement.contactPerson.length < 1) {
                isError = true;
                data['AnnouncementBonus-contactPerson'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_REQUIRED');
            }

            if (!announcement.contactPersonEmail || announcement.contactPersonEmail.length < 1) {
                isError = true;
                data['AnnouncementBonus-contactPersonEmail'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL_REQUIRED');
            }

            if (!announcement.contactPersonTelephone || announcement.contactPersonTelephone.length < 1) {
                isError = true;
                data['AnnouncementBonus-contactPersonTelephone'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_TELEPHONE_REQUIRED');
            }

            if (announcement.usersPerGroup < 1) {
                isError = true;
                data['AnnouncementBonus-usersPerGroup'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY');
            }

            if (!announcement.didacticGuide) {
                isError = true;
                data['AnnouncementGeneralInfo-didacticGuide'] = true;
                data.i18n.push('ANNOUNCEMENT.FORM.DIDACTIC_GUIDE.REQUIRED_STEP_2');
            }

            /** Check if email is valid **/
            if (announcement.contactPersonEmail.length > 0 && !validEmail(announcement.contactPersonEmail)) {
                isError = true;
                data['AnnouncementBonus-contactPersonEmail'] = true;
                data.i18n.push('EMAIL.INVALID');
            }

            const keys = Object.keys(data);
            for (let i = 0; i < keys.length; i++) {
                if (keys[i] === 'i18n') continue;
                if (data[keys[i]]) {
                    isError = true;
                    break;
                }
            }

            return { ...error, error: isError, data };
        },
    },
    AnnouncementStudents: {
        enabled: true,
        validate: announcement => {
            let error = {
                type: 'AnnouncementStudents',
                error: false,
                data: {},
            };
            if (announcement.students.length === 0) {
                error.error = true;
                error.message = 'ANNOUNCEMENT.FORM.GROUP.AT_LEAST_ONE_GROUP';
                error.i18n = true;
            } else {
                announcement.students.forEach(group => {
                    if (group.data.length < 1) {
                        error.error = true;
                        error.message = 'ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY';
                        error.i18n = true;
                    }
                })
            }
            return error;
        }
    },
    AnnouncementGroups: {
        enabled: true,
        validate: (announcement, modalities) => {
            let error = {
                type: 'AnnouncementGroups',
                error: false,
                data: {
                    i18n: []
                },
            };

            let message = null;
            const validateSession = announcement.type === COURSE_TYPE_MIXED || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_ON_SITE ||
                                     announcement.type === COURSE_TYPE_MIXED_EXTERN || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN || announcement.type === COURSE_TYPE_ON_SITE_EXTERN;

            const data = {
                i18n: []
            };


            const announcementDStart = Date.parse(announcement.startAt);
            const announcementDFinish = Date.parse(announcement.finishAt);

            let timeInMinutes = 0;

            announcement.students.forEach(groupOfStudents => {
                let error = false;
                if (announcement.subsidized) {
                    data[`UserGroup-${groupOfStudents.id}-code`] = !groupOfStudents.code || groupOfStudents.code.length < 1;
                    if (data[`UserGroup-${groupOfStudents.id}-code`]) error = true;
                }

                if (groupOfStudents.tutor == null || groupOfStudents.tutor.tutorId === undefined || groupOfStudents.tutor.tutorId < 1) {
                    message = 'ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED';
                    error = true;
                }

                if (groupOfStudents.data.length < 1) {
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP.CANNOT_BE_EMPTY');
                    error = true;
                }

                if (groupOfStudents.tutor == null || groupOfStudents.tutor.tutorId === undefined || groupOfStudents.tutor.tutorId < 1) {
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP.TUTOR_REQUIRED');
                    error = true;
                }

                // Validate sessions
                if ((announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_ON_SITE || announcement.type === COURSE_TYPE_MIXED ||
                     announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN || announcement.type === COURSE_TYPE_ON_SITE_EXTERN || announcement.type === COURSE_TYPE_MIXED_EXTERN) && groupOfStudents.sessions.length === 0)
                {
                    error = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSIONS_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSIONS_REQUIRED');
                }

                if (validateSession && groupOfStudents.numberOfSessions > 0) {
                    let prevFinishAt = null;
                    let modalityErrorFound = false;

                    groupOfStudents.sessions.every((session, sessionIndex) => {
                        if (
                            session.startAt.length < 1 ||
                            session.finishAt.length < 1 ||
                            ((announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM || announcement.type === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN) && session.url?.length < 1)
                        ) {
                            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSION_FIELDS_REQUIRED'))
                                data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSION_FIELDS_REQUIRED');
                            data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                            error = true;
                            return false;
                        }

                        const dSessionStart = Date.parse(session.startAt);
                        const dSessionFinish = Date.parse(session.finishAt);

                        if (prevFinishAt != null) {
                            // Check if current start is greater or equal than previous session finishAt
                            if (dSessionStart < prevFinishAt) {
                                data.i18n.push("ANNOUNCEMENT.FORM.GROUP.WRONG_DATE_BETWEEN_SESSIONS");
                                error = true;
                                data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                                return false;
                            }
                        }
                        prevFinishAt = dSessionFinish;

                        if (dSessionStart > dSessionFinish) {
                            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'))
                                data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH');
                            data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                            error = true;
                        }

                        if (
                            dSessionStart < announcementDStart ||
                            dSessionStart > announcementDFinish ||
                            dSessionFinish < announcementDStart ||
                            dSessionFinish > announcementDFinish) {
                            if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.ERROR_SESSION_BETWEEN_ANNOUNCEMENT'))
                                data.i18n.push('ANNOUNCEMENT.FORM.GROUP.ERROR_SESSION_BETWEEN_ANNOUNCEMENT');
                            data[`UserGroup-${groupOfStudents.id}-session-${sessionIndex}`] = true;
                            error = true;
                        }

                        // Validate the time
                        const diffInMilliseconds = dSessionFinish - dSessionStart;
                        let diffInMinutes = (diffInMilliseconds / 1000) / 60;
                        timeInMinutes += diffInMinutes;

                        if ((announcement.type === COURSE_TYPE_ON_SITE || session.type === 'PRESENTIAL') && modalities?.length && !session.modality && !modalityErrorFound) {
                            session.modality = {
                                "id": 1,
                                "name": "Presencial"
                            }
                        }
                        return true;
                    })
                }

                data[`AnnouncementGroups-${groupOfStudents.id}`] = error;
            });



            const keys = Object.keys(data);
            let isError = false;

            // No error so far, check for the number of hours against total formation time
            const timeInHours = timeInMinutes / 60;
            if ((announcement.type === COURSE_TYPE_ON_SITE || announcement.type === COURSE_TYPE_ON_SITE_EXTERN) && timeInHours < announcement.totalHours) {
                isError = true;
                if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP.SESSION_TIME_AND_FORMATION_TIME_FAILED'))
                    data.i18n.push('ANNOUNCEMENT.FORM.GROUP.SESSION_TIME_AND_FORMATION_TIME_FAILED');
            }


            for (let i = 0; i < keys.length; i++) {
                if (data[keys[i]] === true) {
                    isError = true;
                    break;
                }
            }

            return {...error, error: isError, data, message, i18n: true};
        },
    },

    AnnouncementTutor: {
        enabled: true,
        validate: (announcement, formData, newTutor = false) => {
            const isFormData = formData instanceof FormData;
            let error = {
                type: 'AnnouncementTutor',
                error: false,
                data: {
                    i18n: []
                },
            };
            const data = {
                i18n: []
            };
            let isError = false;
            if (newTutor) {
                const firstName = isFormData ? formData.get('firstName') : formData.firstName;
                const lastName = isFormData ? formData.get('lastName') : formData.lastName;
                if (firstName == null || firstName.length === 0) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED');
                }
                if (lastName == null || lastName.length === 0) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED');
                }
            } else {
                const tutorId = isFormData ? formData.get('tutor-id') : formData['tutor-id'];
                if (tutorId == null || tutorId.length === 0) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED');
                }
            }
            const email = isFormData ? formData.get('email') : formData.email;
            if (email == null || email.length === 0) {
                isError = true;
                if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED'))
                    data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.FIRSTNAME_REQUIRED');
            }

            if (announcement.subsidized) {
                // Require other fields
                const identification = isFormData ? formData.get('identification') : formData.identification;
                if (identification == null || identification.id === null || identification.id < 1) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.IDENTIFICATION_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.IDENTIFICATION_REQUIRED');
                }

                const identificationValue = isFormData ? formData.get('identificationValue') : formData.identificationValue;
                if (identificationValue == null || identificationValue.length === 0) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.IDENTIFICATION_VALUE_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.IDENTIFICATION_VALUE_REQUIRED');
                }

                const telephone = isFormData ? formData.get('telephone') : formData.telephone;
                if (telephone == null || telephone.length === 0) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.TELEPHONE_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.TELEPHONE_REQUIRED');
                }

                const cv = isFormData ? formData.get('cv') : formData.cv;
                if (cv == null) {
                    isError = true;
                    if (!data.i18n.includes('ANNOUNCEMENT.FORM.GROUP_TUTOR.CV_REQUIRED'))
                        data.i18n.push('ANNOUNCEMENT.FORM.GROUP_TUTOR.CV_REQUIRED');
                }
            }

            return {...error, error: isError, data};
        }
    }
};

export const COMPONENT_CONFIGURATION = {
    'AnnouncementGeneralInfo': {
        icon: 'fa fa-bullhorn',
        save: true,
        saveMethodName: 'saveAnnouncementGeneralInfo'//'saveGeneralInfo'
    },
    'AnnouncementBonus': {
        icon: 'fa fa-coins',
        save: true,
        saveMethodName: 'saveAnnouncementBonus'//'saveBonification'
    },
    'AnnouncementStudents': {
        icon: 'fa fa-users',
        save: true,
        saveMethodName: 'saveAnnouncementStudents'//'saveStudents'
    },
    'AnnouncementGroups': {
        icon: 'fa fa-users',
        save: true,
        saveMethodName: 'saveAnnouncementGroups'//'saveGroupGeneralInfo'
    },
    'AnnouncementCommunication': {
        icon: 'fa fa-envelope',
        save: true,
        saveMethodName: 'saveAnnouncementCommunication'//'sendCommunicationStatus'
    },
    'AnnouncementSurvey': {
        icon: 'fa fa-bullhorn',
        save: true,
        saveMethodName: 'saveAnnouncementSurvey'//'sendSurveyStatus'
    },
    'AnnouncementCertificate': {
        icon: 'fa fa-bullhorn',
        save: true,
        saveMethodName: 'saveAnnouncementCertificate'//'sendCertificateStatus'
    },
    'AnnouncementAlerts': {
        icon: 'fa fa-bullhorn',
        save: true,
        saveMethodName: 'saveAnnouncementAlerts'//'sendAlertsStatus'
    },

    /** Extern type methods **/
    'AnnouncementGeneralInfoExtern': {
        icon: 'fa fa-bullhorn',
        save: true,
        saveMethodName: 'saveAnnouncementGeneralInfoExtern'
    }
};


/**
 * Methods
 */

/**
 *
 * @param callback
 * @param source
 * @returns {Promise<void | {trace: *, data: string, error: boolean}>}
 */
export function loadPreData(callback, source = SOURCE_ANNOUNCEMENT_INTERN) {
    return axios.get('/admin/announcement/form/pre-data?source=' + source)
        .then(r => {
            if (typeof callback === 'function') callback(r.data);
        })
        .catch(e => ({error: true, data: 'Failed to request data', trace: e}));
}

/**
 * Load all announcement tutors
 * @returns {Promise<axios.AxiosResponse<any> | {trace: *, data: string, error: boolean}>}
 */
export function loadTutors() {
    return axios.get('/admin/announcement/form/tutors').then(r => (r.data.data))
        .catch(e => ({error: true, data: 'Failed to make request', trace: e}));
}

/**
 * @param id
 * @returns {Promise<{data: *, error: boolean} | {trace: *, data: string, error: boolean}>}
 */
export function loadAnnouncement(id) {
    return axios.get(`/admin/announcement/form/announcement/${id}`)
        .then(r => {
            const {data} = r.data;
            // Repair dates
            data.finishAt = data.finishAt?.slice(0, 19);
            data.startAt = data.startAt?.slice(0, 19);
            const timingKeys = Object.keys(data.chapterTiming);
            timingKeys.forEach(k => {
                data.chapterTiming[k].start = data.chapterTiming[k].start?.slice(0, 19);
                data.chapterTiming[k].end = data.chapterTiming[k].end?.slice(0, 19);
                data.chapterTiming[k].offset = data.chapterTiming[k].start?.slice(19, 25);
            })
            data.students = data.students.map(group => {
                group.sessions = group.sessions.map(session => {
                    return {
                        ...session,
                        startAt: session?.startAt?.slice(0, 19),
                        finishAt: session?.finishAt?.slice(0, 19),
                        offset: session?.startAt?.slice(19, 25)
                    };
                })
                return {...group};
            });
            return {error: false, data};
        })
        .catch(e => ({error: true, data: 'Failed to load announcement', trace: e}));
}

export function configureSteps(stepsConfigurations, source = SOURCE_ANNOUNCEMENT_INTERN) {
    const defaultConfiguration = COMPONENT_CONFIGURATION

    const keys = Object.keys(stepsConfigurations);
    let data = {};
    keys.forEach( type => {
        const info = {
            titles: new Map(),
            steps: [],
        };

        let step = 1;
        stepsConfigurations[type].forEach(stepInfo => {
            let componentName = null;
            if ('component' in stepInfo.extra && stepInfo.extra.component != null) {
                componentName = stepInfo.extra.component;
            }
            else {
                const intern = SOURCE_ANNOUNCEMENT_INTERN.toUpperCase();
                const extern = SOURCE_ANNOUNCEMENT_EXTERN.toUpperCase();
                if (source === SOURCE_ANNOUNCEMENT_INTERN && `${intern}` in stepInfo.extra && stepInfo.extra[intern].component != null) {
                    componentName = stepInfo.extra[intern].component;
                }
                else if (source === SOURCE_ANNOUNCEMENT_EXTERN && `${extern}` in stepInfo.extra && stepInfo.extra[extern].component != null) {
                    componentName = stepInfo.extra[extern].component;
                }
            }
            info.titles.set(step, stepInfo.name);
            const objectInfo = {...{type: componentName, configurations: stepInfo.configurations, step}, ...defaultConfiguration[componentName] ?? {}};
            info.steps.push(objectInfo);
            step += 1;
        });

        data[type] = info;
    });

    return data;
}

export function initAnnouncement(source = SOURCE_ANNOUNCEMENT_INTERN) {
    let announcement = ANNOUNCEMENT_DEFAULT_VALUES;
    if (source === SOURCE_ANNOUNCEMENT_EXTERN) {
        announcement.source = SOURCE_ANNOUNCEMENT_EXTERN;
        announcement.students = [{
            data: [],
            tutor: {
                name: "",
                email: "",
            },
            place: '',
            cost: 0,
            typeMoney: null
        }];
        announcement.course = {
            name: '',
            category: {},
            locale: 'es',
            typeCourseId: null
        }
    }
    return announcement;
}

export function initAnnouncementApprovedCriteria(approvedCriteria) {
    const data = {};
    approvedCriteria.forEach((criteria) => {
        if (criteria.extra.alwaysEnabled) {
            data[criteria.id] = {
                value: 0,
                enabled: false,
                alwaysEnabled: true
            };
        } else {
            data[criteria.id] = {
                value: 0,
                enabled: false,
            };
        }
    });
    return data;
}

export function getCourseChapters(courseId, chapterTiming) {
    return axios.get(`/admin/announcement/form/chapters/${courseId}`)
        .then(r => {
            const { data } = r.data;
            const chapterTimingKeys = Object.keys(chapterTiming);

            let timing = {};
            if (chapterTimingKeys.length === 0) {
                data.forEach((chapter) => {
                    timing[chapter.id] = {
                        title: chapter.title,
                        type: chapter.type,
                        start: '',
                        end: '',
                        time: '00:00:00',
                    };
                });
            } else {
                data.forEach(chapter => {
                    const find = chapterTimingKeys.find(key => parseInt(key) === chapter.id);
                    timing[chapter.id] = {
                        title: chapter.title ?? '',
                        type: chapter.type ?? '',
                        start: chapterTiming[find]?.start?.slice(0, 16),
                        end: chapterTiming[find]?.end?.slice(0, 16),
                        time: chapterTiming[find]?.time ?? '00:00:00'
                    };
                })
            }

            return { timing, chapters: data};
        })
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

export function findCourses({ query, typeCourse, page = 1, pageSize = 10, coursesStatus }) {
    const url = new URL(window.location.origin + '/admin/announcement/form/courses');
    url.searchParams.set('query', query);
    url.searchParams.set('typeCourse', typeCourse);
    url.searchParams.set('page', `${page}`);
    url.searchParams.set('page-size', `${pageSize}`);
    url.searchParams.set('coursesStatus', `${coursesStatus}`);
    return axios.get(url.toString()).then(r=> (r.data))
        .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
}

