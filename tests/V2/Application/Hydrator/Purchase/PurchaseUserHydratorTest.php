<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\Purchase;

use App\Entity\User;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\Purchase\Exception\PurchaseHydratorException;
use App\V2\Application\Hydrator\Purchase\PurchaseUserHydrator;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\User\UserCollection;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PurchaseUserHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?UserRepository $userRepository = null,
    ): PurchaseUserHydrator {
        return new PurchaseUserHydrator(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    private function createMockUser(): User
    {
        return $this->createMock(User::class);
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();

        $this->assertTrue(
            $hydrator->supports(
                PurchaseHydrationCriteria::createEmpty()->withUser()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                PurchaseHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws Exception
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testEmptyCollection(): void
    {
        $collection = new PurchaseCollection([]);
        $hydrator = $this->getHydrator();

        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrate(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();
        $purchase3 = PurchaseMother::create();

        $user1 = $this->createMockUser();
        $user2 = $this->createMockUser();
        $user3 = $this->createMockUser();

        // Mock the getId method on users to return the purchase user IDs
        $user1->method('getId')->willReturn($purchase1->getUserId()->value());
        $user2->method('getId')->willReturn($purchase2->getUserId()->value());
        $user3->method('getId')->willReturn($purchase3->getUserId()->value());

        $collection = new PurchaseCollection([$purchase1, $purchase2, $purchase3]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new UserCollection([$user1, $user2, $user3]));

        $hydrator = $this->getHydrator(userRepository: $userRepository);

        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertEquals($user1, $purchase1->getUser());
        $this->assertEquals($user2, $purchase2->getUser());
        $this->assertEquals($user3, $purchase3->getUser());
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrateWithEmptyUserCollection(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();

        $collection = new PurchaseCollection([$purchase1, $purchase2]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new UserCollection([]));

        $hydrator = $this->getHydrator(userRepository: $userRepository);

        // Should not throw any exception and should not call setUser
        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withUser()
        );

        // Verify that users were not set
        $this->assertNull($purchase1->getUser());
        $this->assertNull($purchase2->getUser());
    }

    /**
     * @throws Exception
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrateWithNonPurchaseCollection(): void
    {
        $collection = $this->createMock(\App\V2\Domain\Shared\Collection\Collection::class);
        $hydrator = $this->getHydrator();

        // Should return early without doing anything
        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withUser()
        );

        // Test passes if no exception is thrown
        $this->assertTrue(true);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws PurchaseHydratorException
     */
    public function testHydrateWithPartialUserMatch(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();
        $purchase3 = PurchaseMother::create();

        $user1 = $this->createMockUser();
        $user2 = $this->createMockUser();
        // Note: user3 is missing, so purchase3 should not get a user

        // Mock the getId method on users to return the purchase user IDs
        $user1->method('getId')->willReturn($purchase1->getUserId()->value());
        $user2->method('getId')->willReturn($purchase2->getUserId()->value());

        $collection = new PurchaseCollection([$purchase1, $purchase2, $purchase3]);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new UserCollection([$user1, $user2])); // Only 2 users returned

        $hydrator = $this->getHydrator(userRepository: $userRepository);

        $hydrator->hydrate(
            $collection,
            PurchaseHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertEquals($user1, $purchase1->getUser());
        $this->assertEquals($user2, $purchase2->getUser());
        $this->assertNull($purchase3->getUser()); // This purchase should not have a user
    }
}
