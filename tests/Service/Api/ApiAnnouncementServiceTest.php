<?php

declare(strict_types=1);

namespace App\Tests\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\Chapter;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\ChapterContent;
use App\Repository\AnnouncementGroupSessionRepository;
use App\Repository\AnnouncementTemporalizationRepository;
use App\Repository\AnnouncementUserDigitalSignatureRepository;
use App\Repository\AnnouncementUserRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementGroupAssistanceService;
use App\Service\Api\ApiAnnouncementService;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementGroupSessionMother;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\AnnouncementUserMother;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\ChapterTypeMother;
use App\Tests\Mother\Entity\ContentMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\SeasonMother;
use App\Tests\Mother\Entity\TypeCourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\Rule\InvokedCount;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class ApiAnnouncementServiceTest extends TestCase
{
    private function getService(
        ?EntityManagerInterface $em = null,
        ?SettingsService $settingsService = null,
        ?AnnouncementConfigurationsService $announcementConfigurationsService = null,
        ?AnnouncementGroupAssistanceService $announcementGroupAssistanceService = null,
        ?Security $security = null,
        ?GeolocationService $geolocationService = null,
        ?RequestStack $requestStack = null
    ): ApiAnnouncementService {
        return new ApiAnnouncementService(
            $em ?? $this->createMock(EntityManagerInterface::class),
            $settingsService ?? $this->createMock(SettingsService::class),
            $announcementConfigurationsService ?? $this->createMock(AnnouncementConfigurationsService::class),
            $announcementGroupAssistanceService ?? $this->createMock(AnnouncementGroupAssistanceService::class),
            $security ?? $this->createMock(Security::class),
            $geolocationService ?? $this->createMock(GeolocationService::class),
            $requestStack ?? $this->createMock(RequestStack::class),
            $this->createMock(VirtualMeetingRepository::class),
        );
    }

    private function getSecurityMock(User $user, ?InvokedCount $invokedCount = null): Security
    {
        $security = $this->createMock(Security::class);
        $security->expects($invokedCount ?: $this->once())
            ->method('getUser')
            ->willReturn($user);

        return $security;
    }

    private function getAnnouncementUserRepositoryMock(
        ?AnnouncementUser $announcementUser,
        ?InvokedCount $invokedCount = null
    ): AnnouncementUserRepository {
        $announcementUserRepository = $this->createMock(AnnouncementUserRepository::class);
        $announcementUserRepository->expects($invokedCount ?: $this->once())
            ->method('findOneBy')
            ->willReturn($announcementUser);

        return $announcementUserRepository;
    }

    private function getEntityManagerMock(
        ?AnnouncementUserRepository $announcementUserRepository = null,
        ?AnnouncementGroupSessionRepository $announcementGroupSessionRepository = null,
        ?AnnouncementUserDigitalSignatureRepository $announcementUserDigitalSignatureRepository = null
    ): EntityManagerInterface {
        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->willReturnCallback(function ($entity) use (
                $announcementUserRepository,
                $announcementGroupSessionRepository,
                $announcementUserDigitalSignatureRepository
            ) {
                if (AnnouncementUser::class === $entity) {
                    return $announcementUserRepository;
                }

                if (AnnouncementGroupSession::class === $entity) {
                    return $announcementGroupSessionRepository;
                }

                if (AnnouncementUserDigitalSignature::class === $entity) {
                    return $announcementUserDigitalSignatureRepository;
                }

                return null;
            });

        return $em;
    }

    /**
     * @dataProvider courseHasSignatureDigitalPendingNoCorrectAnnouncementUserDataProvider
     */
    public function testCourseHasSignatureDigitalPendingNoCorrectAnnouncementUser(
        ?AnnouncementUser $announcementUser
    ): void {
        $user = new User();
        $security = $this->getSecurityMock($user);
        $announcementUserRepository = $this->getAnnouncementUserRepositoryMock($announcementUser);
        $em = $this->getEntityManagerMock($announcementUserRepository);
        $service = $this->getService(
            $em,
            null,
            null,
            null,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public static function courseHasSignatureDigitalPendingNoCorrectAnnouncementUserDataProvider(): \Generator
    {
        yield 'No announcement user' => [
            null,
        ];

        yield 'Announcement user without announcement group' => [
            new AnnouncementUser(),
        ];
    }

    public function testCourseHasSignatureDigitalPendingWithoutSessions(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $em = $this->getEntityManagerMock($this->getAnnouncementUserRepositoryMock($announcementUser));

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([]);

        $service = $this->getService(
            $em,
            null,
            null,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithoutDigitalSignature(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                ],
                [
                    'id' => 2,
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser),
            $announcementGroupSessionRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(false);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionsNotInProcess(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'OTHER',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository->expects($this->once())
            ->method('findBy')
            ->willReturnCallback(
                function ($criteria) {
                    if (isset($criteria['id'])) {
                        $sessions = [];
                        foreach ($criteria['id'] as $id) {
                            $sessions[] = new AnnouncementGroupSession();
                        }

                        return $sessions;
                    }

                    return null;
                }
            );

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser),
            $announcementGroupSessionRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());

        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionInProcess(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user, $this->exactly(2));

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'IN_PROCESS',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository
            ->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);
        $announcementGroupSessionRepository
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn(new AnnouncementGroupSession());

        $announcementUserDigitalSignatureRepository = $this->createMock(
            AnnouncementUserDigitalSignatureRepository::class
        );
        $announcementUserDigitalSignatureRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(new AnnouncementUserDigitalSignature());

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser, $this->exactly(2)),
            $announcementGroupSessionRepository,
            $announcementUserDigitalSignatureRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionInProcessAndSignatureNotFound(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user, $this->exactly(2));

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'IN_PROCESS',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository
            ->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);
        $announcementGroupSessionRepository
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn(new AnnouncementGroupSession());

        $announcementUserDigitalSignatureRepository = $this->createMock(
            AnnouncementUserDigitalSignatureRepository::class
        );
        $announcementUserDigitalSignatureRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(null);

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser, $this->exactly(2)),
            $announcementGroupSessionRepository,
            $announcementUserDigitalSignatureRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertTrue($service->theCourseHasSignatureDigitalPending($announcement));
    }

    /**
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    public function testGetTemporalizationAnnouncement(): void
    {
        $course = CourseMother::create();
        $announcement = AnnouncementMother::create(
            course: $course,
            startAt: new \DateTimeImmutable('-1 day'),
            finishAt: new \DateTimeImmutable('+1 day'),
        );
        $season1 = SeasonMother::create(
            id: 1,
            name: 'Season 1',
            sort: 1,
            course: $course,
        );
        $season2 = SeasonMother::create(
            id: 2,
            name: 'Season 2',
            sort: 2,
            course: $course,
        );

        $course
            ->addSeason($season1)
            ->addSeason($season2);

        $chapterType = ChapterTypeMother::create(
            id: ChapterContent::CONTENT_TYPE,
            name: 'Content',
        );

        $chapter1 = ChapterMother::create(
            title: 'Chapter 1',
            position: 1,
            type: $chapterType,
            season: $season1,
        );
        $chapter2 = ChapterMother::create(
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            season: $season1,
        );
        $chapter3 = ChapterMother::create(
            title: 'Chapter 3',
            position: 1,
            type: $chapterType,
            season: $season2,
        );
        $chapter4 = ChapterMother::create(
            title: 'Chapter 4',
            position: 2,
            type: $chapterType,
            season: $season2,
        );
        $chapter5 = ChapterMother::create(
            title: 'Chapter 5',
            position: 3,
            type: $chapterType,
            season: $season1,
        );

        $chapter1->addContent(ContentMother::create(id: 1, chapter: $chapter1));
        $chapter2->addContent(ContentMother::create(id: 2, chapter: $chapter2));
        $chapter3->addContent(ContentMother::create(id: 3, chapter: $chapter3));
        $chapter4->addContent(ContentMother::create(id: 4, chapter: $chapter4));
        $chapter5->addContent(ContentMother::create(id: 5, chapter: $chapter5));

        $course
            ->addChapter($chapter1)
            ->addChapter($chapter2)
            ->addChapter($chapter3)
            ->addChapter($chapter4)
            ->addChapter($chapter5);

        $userCourse = $this->createMock(UserCourse::class);
        $announcementTemporalization = $this->createMock(AnnouncementTemporalization::class);
        $userCourseChapter = $this->createMock(UserCourseChapter::class);

        // Setup request mock
        $request = $this->createMock(Request::class);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $request->method('get')->with('new', false)->willReturn(false);
        $request->method('getSchemeAndHttpHost')->willReturn('https://example.com');

        // Setup repositories
        $announcementTemporalizationRepo = $this->createMock(EntityRepository::class);
        $announcementTemporalizationRepo->method('findOneBy')->willReturn($announcementTemporalization);

        $userCourseChapterRepo = $this->createMock(EntityRepository::class);
        $userCourseChapterRepo->method('findOneBy')->willReturn($userCourseChapter);

        $userCourseRepo = $this->createMock(EntityRepository::class);
        $userCourseRepo->method('findOneBy')->willReturn($userCourse);

        $chapterRepository = $this->createMock(EntityRepository::class);
        $chapterRepository->method('findBy')->willReturn($course->getChapters()->toArray());

        // Setup entity manager
        $repositories = [
            AnnouncementTemporalization::class => $announcementTemporalizationRepo,
            UserCourseChapter::class => $userCourseChapterRepo,
            UserCourse::class => $userCourseRepo,
            Chapter::class => $chapterRepository,
        ];
        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnCallback(function ($entityClass) use ($repositories) {
            return $repositories[$entityClass] ?? $this->createMock(EntityRepository::class);
        });

        // Setup settings service
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')->willReturn('UTC');

        // Setup announcement configurations service
        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->method('hasTemporalization')->willReturn(true);

        $service = $this->getService(
            em: $em,
            settingsService: $settingsService,
            announcementConfigurationsService: $announcementConfigurationsService,
            geolocationService: $this->createMock(GeolocationService::class),
            requestStack: $requestStack,
        );

        // Call method
        $result = $service->getTemporalizationAnnouncement($announcement);

        // Assertions
        $this->assertIsArray($result);

        $this->assertCount(5, $result);
        $expectedOrder = [
            'Chapter 1',
            'Chapter 2',
            'Chapter 5',
            'Chapter 3',
            'Chapter 4',
        ];

        $resultOrder = array_map(fn ($chapter) => $chapter['title'], $result);

        $this->assertSame($expectedOrder, $resultOrder);
    }

    /**
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    public function testGetTemporalizationAnnouncementWithChaptersWithoutSeasons(): void
    {
        $course = CourseMother::create();
        $announcement = AnnouncementMother::create(
            course: $course,
            startAt: new \DateTimeImmutable('-1 day'),
            finishAt: new \DateTimeImmutable('+1 day'),
        );
        $season1 = SeasonMother::create(
            id: 1,
            name: 'Season 1',
            sort: 1,
            course: $course,
        );
        $season2 = SeasonMother::create(
            id: 2,
            name: 'Season 2',
            sort: 2,
            course: $course,
        );

        $course
            ->addSeason($season1)
            ->addSeason($season2);

        $chapterType = ChapterTypeMother::create(
            id: ChapterContent::CONTENT_TYPE,
            name: 'Content',
        );

        // Chapters with seasons
        $chapter1 = ChapterMother::create(
            title: 'Chapter 1',
            position: 1,
            type: $chapterType,
            season: $season1,
        );
        $chapter2 = ChapterMother::create(
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            season: $season1,
        );
        $chapter3 = ChapterMother::create(
            title: 'Chapter 3',
            position: 1,
            type: $chapterType,
            season: $season2,
        );

        // Chapters without seasons
        $chapter4 = ChapterMother::create(
            title: 'Chapter 4',
            position: 2,
            type: $chapterType,
            course: $course,
            season: null,
        );
        $chapter5 = ChapterMother::create(
            title: 'Chapter 5',
            position: 3,
            type: $chapterType,
            course: $course,
            season: null,
        );

        $chapter1->addContent(ContentMother::create(id: 1, chapter: $chapter1));
        $chapter2->addContent(ContentMother::create(id: 2, chapter: $chapter2));
        $chapter3->addContent(ContentMother::create(id: 3, chapter: $chapter3));
        $chapter4->addContent(ContentMother::create(id: 4, chapter: $chapter4));
        $chapter5->addContent(ContentMother::create(id: 5, chapter: $chapter5));

        $course
            ->addChapter($chapter1)
            ->addChapter($chapter2)
            ->addChapter($chapter3)
            ->addChapter($chapter4)
            ->addChapter($chapter5);

        $userCourse = $this->createMock(UserCourse::class);
        $announcementTemporalization = $this->createMock(AnnouncementTemporalization::class);
        $userCourseChapter = $this->createMock(UserCourseChapter::class);

        // Setup request mock
        $request = $this->createMock(Request::class);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $request->method('get')->with('new', false)->willReturn(false);
        $request->method('getSchemeAndHttpHost')->willReturn('https://example.com');

        // Setup repositories
        $announcementTemporalizationRepo = $this->createMock(EntityRepository::class);
        $announcementTemporalizationRepo->method('findOneBy')->willReturn($announcementTemporalization);

        $userCourseChapterRepo = $this->createMock(EntityRepository::class);
        $userCourseChapterRepo->method('findOneBy')->willReturn($userCourseChapter);

        $userCourseRepo = $this->createMock(EntityRepository::class);
        $userCourseRepo->method('findOneBy')->willReturn($userCourse);

        $chapterRepository = $this->createMock(EntityRepository::class);
        $chapterRepository->method('findBy')->willReturn([
            $chapter1,
            $chapter3,
            $chapter2,
            $chapter4,
            $chapter5,
        ]);

        // Setup entity manager
        $repositories = [
            AnnouncementTemporalization::class => $announcementTemporalizationRepo,
            UserCourseChapter::class => $userCourseChapterRepo,
            UserCourse::class => $userCourseRepo,
            Chapter::class => $chapterRepository,
        ];
        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnCallback(function ($entityClass) use ($repositories) {
            return $repositories[$entityClass] ?? $this->createMock(EntityRepository::class);
        });

        // Setup settings service
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')->willReturn('UTC');

        // Setup announcement configurations service
        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->method('hasTemporalization')->willReturn(true);

        $service = $this->getService(
            em: $em,
            settingsService: $settingsService,
            announcementConfigurationsService: $announcementConfigurationsService,
            geolocationService: $this->createMock(GeolocationService::class),
            requestStack: $requestStack,
        );

        // Call method
        $result = $service->getTemporalizationAnnouncement($announcement);

        // Assertions
        $this->assertIsArray($result);

        $this->assertCount(5, $result);
        // With our fix, chapters with seasons should come first (sorted by season sort value and then position),
        // followed by chapters without seasons (sorted by position)
        $expectedOrder = [
            'Chapter 1',
            'Chapter 2',
            'Chapter 3',
            'Chapter 4',
            'Chapter 5',
        ];

        $resultOrder = array_map(fn ($chapter) => $chapter['title'], $result);

        $this->assertSame($expectedOrder, $resultOrder);
    }

    /**
     * @throws \DateMalformedStringException
     * @throws \DateInvalidTimeZoneException
     * @throws Exception
     */
    #[DataProvider('getTemporalizationAndSessionsAnnouncementCorrectOrderDataProvider')]
    public function testGetTemporalizationAndSessionsAnnouncementCorrectOrder(
        bool $hasTemporalization,
        int $sessionId,
        \DateTimeImmutable $sessionStartAt,
        \DateTimeImmutable $sessionFinishAt,
        int $chapter1Id,
        ?\DateTimeImmutable $chapter1StartAt,
        ?\DateTimeImmutable $chapter1FinishAt,
        int $chapter2Id,
        ?\DateTimeImmutable $chapter2StartAt,
        ?\DateTimeImmutable $chapter2FinishAt,
        array $expectedIdsInOrder,
    ): void {
        $user = UserMother::create();

        $course = CourseMother::create(
            typeCourse: TypeCourseMother::create(id: \App\Entity\TypeCourse::TYPE_MIXTO)
        );

        $season1 = SeasonMother::create(
            id: 1,
            name: 'Season 1',
            sort: 1,
            course: $course,
        );

        $course
            ->addSeason($season1);

        $chapterType = ChapterTypeMother::create(
            id: ChapterContent::CONTENT_TYPE,
            name: 'Content',
        );

        // Chapters with seasons
        $chapter1 = ChapterMother::create(
            id: $chapter1Id,
            title: 'Chapter 1',
            position: 1,
            type: $chapterType,
            season: $season1,
        );
        $chapter2 = ChapterMother::create(
            id: $chapter2Id,
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            season: $season1,
        );

        $chapter1->addContent(ContentMother::create(id: 1, chapter: $chapter1));
        $chapter2->addContent(ContentMother::create(id: 2, chapter: $chapter2));

        $course
            ->addChapter($chapter1)
            ->addChapter($chapter2);

        $announcement = AnnouncementMother::create(
            course: $course,
            startAt: new \DateTimeImmutable('-1 day'),
            finishAt: new \DateTimeImmutable('+1 day'),
        );

        // Sessions
        $announcementSession = AnnouncementGroupSessionMother::create(
            id: $sessionId,
            startAt: $sessionStartAt,
            finishAt: $sessionFinishAt,
        );

        // Setup request mock
        $request = $this->createMock(Request::class);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $request->method('get')->with('new', false)->willReturn(false);
        $request->method('getSchemeAndHttpHost')->willReturn('https://example.com');

        $chapterRepository = $this->createMock(EntityRepository::class);
        $chapterRepository->method('findBy')->willReturn([
            $chapter1,
            $chapter2,
        ]);

        $announcementUser = AnnouncementUserMother::create(
            announcement: $announcement,
            user: $user,
        );
        $announcementUserRepository = $this->getAnnouncementUserRepositoryMock(
            $announcementUser,
        );

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository->method('findBy')
            ->willReturn([$announcementSession]);

        $announcementTemporalizationRepository = $this->createMock(AnnouncementTemporalizationRepository::class);
        if ($hasTemporalization) {
            $chapter1Temporalization = $this->createMock(AnnouncementTemporalization::class);
            $chapter1Temporalization->method('getStartedAt')->willReturn($chapter1StartAt);
            $chapter1Temporalization->method('getFinishedAt')->willReturn($chapter1FinishAt);

            $chapter2Temporalization = $this->createMock(AnnouncementTemporalization::class);
            $chapter2Temporalization->method('getStartedAt')->willReturn($chapter2StartAt);
            $chapter2Temporalization->method('getFinishedAt')->willReturn($chapter2FinishAt);

            $announcementTemporalizationRepository->method('findOneBy')
                ->willReturn($chapter1Temporalization, $chapter2Temporalization);
        }

        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnCallback(function ($entityClass) use (
            $chapterRepository,
            $announcementUserRepository,
            $announcementGroupSessionRepository,
            $announcementTemporalizationRepository
        ) {
            if (Chapter::class === $entityClass) {
                return $chapterRepository;
            }

            if (AnnouncementUser::class === $entityClass) {
                return $announcementUserRepository;
            }

            if (AnnouncementGroupSession::class === $entityClass) {
                return $announcementGroupSessionRepository;
            }

            if (AnnouncementTemporalization::class === $entityClass) {
                return $announcementTemporalizationRepository;
            }

            return $this->createMock(EntityRepository::class);
        });

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->method('hasTemporalization')->willReturn($hasTemporalization);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->method('getSessions')
            ->willReturn([
                [
                    'id' => $announcementSession->getId(),
                    'startAt' => $announcementSession->getStartAt(),
                    'finishAt' => $announcementSession->getFinishAt(),
                    'state' => 'IN_PROCESS',
                    'virtual_meeting_id' => null,
                ],
            ]);

        $apiAnnouncementService = $this->getService(
            em: $em,
            announcementConfigurationsService: $announcementConfigurationsService,
            announcementGroupAssistanceService: $announcementGroupAssistanceService,
            requestStack: $requestStack,
        );

        $dataChapters = $apiAnnouncementService->getTemporalizationAndSessionsAnnouncement(
            announcement: $announcement,
            user: $user,
        );

        $this->assertIsArray($dataChapters);

        $this->assertCount(3, $dataChapters);

        if (!$hasTemporalization) {
            $this->assertInstanceOf(Chapter::class, $dataChapters[0]);
            $this->assertInstanceOf(Chapter::class, $dataChapters[1]);
            $this->assertIsArray($dataChapters[2]);
            $this->assertEquals($expectedIdsInOrder[0], $dataChapters[0]->getId());
            $this->assertEquals($expectedIdsInOrder[1], $dataChapters[1]->getId());
            $this->assertEquals($expectedIdsInOrder[2], $dataChapters[2]['id']);
        } else {
            $this->assertArrayHasKey('id', $dataChapters[0]);
            $this->assertArrayHasKey('id', $dataChapters[1]);
            $this->assertArrayHasKey('id', $dataChapters[2]);
            $this->assertEquals($expectedIdsInOrder[0], $dataChapters[0]['id']);
            $this->assertEquals($expectedIdsInOrder[1], $dataChapters[1]['id']);
            $this->assertEquals($expectedIdsInOrder[2], $dataChapters[2]['id']);
        }
    }

    public static function getTemporalizationAndSessionsAnnouncementCorrectOrderDataProvider(): \Generator
    {
        yield 'Without temporalization' => [
            false,
            'sessionId' => 1001,
            'sessionStartAt' => (new \DateTimeImmutable('today'))->setTime(10, 0),
            'sessionFinishAt' => (new \DateTimeImmutable('today'))->setTime(12, 0),
            'chapter1Id' => 1,
            'chapter1StartAt' => null,
            'chapter1FinishAt' => null,
            'chapter2Id' => 2,
            'chapter2StartAt' => null,
            'chapter2FinishAt' => null,
            'expectedIdsInOrder' => [1, 2, 1001],
        ];

        yield 'With temporalization chapters before session' => [
            'hasTemporalization' => true,
            'sessionId' => 1001,
            'sessionStartAt' => (new \DateTimeImmutable('today'))->setTime(10, 0),
            'sessionFinishAt' => (new \DateTimeImmutable('today'))->setTime(12, 0),
            'chapter1Id' => 1,
            'chapter1StartAt' => (new \DateTimeImmutable('today'))->setTime(8, 0),
            'chapter1FinishAt' => (new \DateTimeImmutable('today'))->setTime(9, 0),
            'chapter2Id' => 2,
            'chapter2StartAt' => (new \DateTimeImmutable('today'))->setTime(9, 0),
            'chapter2FinishAt' => (new \DateTimeImmutable('today'))->setTime(10, 0),
            'expectedIdsInOrder' => [1, 2, 1001],
        ];

        yield 'With temporalization session before chapters' => [
            'hasTemporalization' => true,
            'sessionId' => 1001,
            'sessionStartAt' => (new \DateTimeImmutable('today'))->setTime(10, 0),
            'sessionFinishAt' => (new \DateTimeImmutable('today'))->setTime(12, 0),
            'chapter1Id' => 1,
            'chapter1StartAt' => (new \DateTimeImmutable('today'))->setTime(12, 30),
            'chapter1FinishAt' => (new \DateTimeImmutable('today'))->setTime(13, 30),
            'chapter2Id' => 2,
            'chapter2StartAt' => (new \DateTimeImmutable('today'))->setTime(13, 30),
            'chapter2FinishAt' => (new \DateTimeImmutable('today'))->setTime(14, 30),
            'expectedIdsInOrder' => [1001, 1, 2],
        ];

        yield 'With temporalization chapters before and after session' => [
            'hasTemporalization' => true,
            'sessionId' => 1001,
            'sessionStartAt' => (new \DateTimeImmutable('today'))->setTime(10, 0),
            'sessionFinishAt' => (new \DateTimeImmutable('today'))->setTime(12, 0),
            'chapter1Id' => 1,
            'chapter1StartAt' => (new \DateTimeImmutable('today'))->setTime(8, 0),
            'chapter1FinishAt' => (new \DateTimeImmutable('today'))->setTime(9, 0),
            'chapter2Id' => 2,
            'chapter2StartAt' => (new \DateTimeImmutable('today'))->setTime(12, 30),
            'chapter2FinishAt' => (new \DateTimeImmutable('today'))->setTime(13, 30),
            'expectedIdsInOrder' => [1, 1001, 2],
        ];
    }
}
