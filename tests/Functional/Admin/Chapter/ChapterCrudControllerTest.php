<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Chapter;

use App\Controller\Admin\ChapterCrudController;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class ChapterCrudControllerTest extends FunctionalTestCase
{
    use CourseHelperTrait;
    use ChapterHelperTrait;
    use CourseCreatorFixtureTrait;

    private ?User $testUser = null;
    private ?User $otherUser = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();

        $this->testUser = $this->createAndGetUser(
            firstName: 'Test',
            lastName: 'User',
            roles: [User::ROLE_ADMIN],
            email: '<EMAIL>',
        );

        $this->otherUser = $this->createAndGetUser(
            firstName: 'Other',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>',
        );
    }

    public function testBadRequestWhenCourseIdNotProvided(): void
    {
        $uri = $this->generateChapterNewUri();
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: $uri,
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    public function testWhenCourseNotFound(): void
    {
        $uri = $this->generateChapterNewUri(9999);
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: $uri,
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     */
    #[DataProvider('providerAccessDeniedRolesForNewChapter')]
    public function testAccessDeniedRolesForNewChapter(
        array $roles,
        int $expectedStatusCode
    ): void {
        $this->testUser->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);

        $course = $this->createAndGetCourse(
            createdBy: $this->testUser,
        );
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $uri = $this->generateChapterNewUri($course->getId());
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: $uri,
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerAccessDeniedRolesForNewChapter(): \Generator
    {
        yield 'When user with Role Manager try to access to new chapter' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedStatusCode' => Response::HTTP_FORBIDDEN,
        ];

        yield 'When user with Role Tutor try to access to new chapter' => [
            'roles' => [User::ROLE_TUTOR],
            'expectedStatusCode' => Response::HTTP_FORBIDDEN,
        ];

        yield 'Whe user with Role inspector try to access to new chapter' => [
            'roles' => [User::ROLE_INSPECTOR],
            'expectedStatusCode' => Response::HTTP_FOUND,
        ];

        yield 'When user with Role User try to access to new chapter' => [
            'roles' => [User::ROLE_USER],
            'expectedStatusCode' => Response::HTTP_FOUND,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     */
    public function testChapterCreationWhenCreatorNotOwner(
    ): void {
        $this->testUser->setRoles([User::ROLE_CREATOR]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);

        $course = $this->createAndGetCourse(
            createdBy: $this->otherUser,
        );

        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $uri = $this->generateChapterNewUri($course->getId());
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: $uri,
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     */
    public function testEditDeniedForUnauthorizedUser(): void
    {
        $this->testUser->setRoles([User::ROLE_MANAGER]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);

        $course = $this->createAndGetCourse(createdBy: $this->otherUser);
        $chapter = $this->createAndGetChapter(course: $course);

        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $uri = $this->generateChapterEditUri($chapter->getId());
        $response = $this->makeAdminApiRequest(method: 'GET', uri: $uri, bearerToken: $userToken);

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws TransactionRequiredException
     */
    public function testEditDeniedForCreatorNotSharedAndNotChapterCreator(): void
    {
        $this->testUser->setRoles([User::ROLE_CREATOR]);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);

        $course = $this->createAndGetCourse(createdBy: $this->otherUser);
        $chapter = $this->createAndGetChapter(course: $course);

        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $uri = $this->generateChapterEditUri($chapter->getId());
        $response = $this->makeAdminApiRequest(method: 'GET', uri: $uri, bearerToken: $userToken);

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    private function generateChapterNewUri(?int $courseId = null): string
    {
        $urlGenerator = $this->getContainer()->get(AdminUrlGenerator::class);
        $fullUrl = $urlGenerator
            ->unsetAll()
            ->setController(ChapterCrudController::class)
            ->setAction('new')
            ->set('courseId', $courseId)
            ->generateUrl();

        // Extract only the path and query string from the full URL
        $parsedUrl = parse_url($fullUrl);
        $uri = $parsedUrl['path'] . (isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '');

        // Remove /admin prefix since makeAdminApiRequest will add it
        if (str_starts_with($uri, '/admin')) {
            $uri = substr($uri, 6); // Remove '/admin'
        }

        return $uri;
    }

    private function generateChapterEditUri(int $chapterId): string
    {
        $urlGenerator = $this->getContainer()->get(AdminUrlGenerator::class);
        $fullUrl = $urlGenerator
            ->unsetAll()
            ->setController(ChapterCrudController::class)
            ->setAction('edit')
            ->setEntityId($chapterId)
            ->generateUrl();

        $parsedUrl = parse_url($fullUrl);
        $uri = $parsedUrl['path'] . (isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '');
        if (str_starts_with($uri, '/admin')) {
            $uri = substr($uri, 6);
        }

        return $uri;
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            Chapter::class,
        ]);

        // Clean up all test users
        $usersIds = array_filter([
            $this->testUser?->getId(),
            $this->otherUser?->getId(),
        ]);

        if (!empty($usersIds)) {
            $this->hardDeleteUsersByIds($usersIds);
        }

        parent::tearDown();
    }
}
