<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Opinions;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Nps;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendCourseEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;

class GetOpinionsFunctionalTest extends FunctionalTestCase
{
    private const string USER_TEST_OPINION = '<EMAIL>';

    public function testOnlineCourseOpinions(): void
    {
        $userToken = $this->loginAndGetToken();
        $user = $this->getDefaultUser();
        $userTestOpinion = $this->createAndGetUser(email: self::USER_TEST_OPINION);
        $course1 = $this->createAndGetCourse();
        $course2 = $this->createAndGetCourse();

        $userCourse1 = $this->createAndGetUserCourse(user: $user, course: $course1);
        $userCourse2 = $this->createAndGetUserCourse(user: $userTestOpinion, course: $course1);
        $userCourse3 = $this->createAndGetUserCourse(user: $user, course: $course2);
        $userCourse4 = $this->createAndGetUserCourse(user: $userTestOpinion, course: $course2);

        $this->createOpinions(user: $user, text: 'Opinion first course', value: '5', userCourse: $userCourse1);
        $this->createOpinions(user: $userTestOpinion, text: '2 Opinion first course', value: '6', userCourse: $userCourse2);
        $this->createOpinions(user: $user, text: 'Opinion second course', value: '7', userCourse: $userCourse3);
        $this->createOpinions(user: $userTestOpinion, text: '2 Opinion second course', value: '8', userCourse: $userCourse4);

        $expected1 = [
            ['date' => '', 'name' => 'John', 'image' => null, 'rating' => 3, 'text' => '2 Opinion first course'],
            ['date' => '', 'name' => 'Soporte', 'image' => null, 'rating' => 2.5, 'text' => 'Opinion first course'],
        ];

        $expected2 = [
            ['date' => '', 'name' => 'John', 'image' => null, 'rating' => 4, 'text' => '2 Opinion second course'],
            ['date' => '', 'name' => 'Soporte', 'image' => null, 'rating' => 3.5, 'text' => 'Opinion second course'],
        ];

        $request = $this->makeFrontendApiRequest(
            method: 'GET',
            uri: FrontendCourseEndpoints::courseOpinionEndpoint(courseId: $course1->getId()),
            bearerToken: $userToken
        );
        $data = $this->extractResponseData($request);
        $normalizedOpinions = $this->normalizeOpinionsData($data);
        $this->assertEquals($expected1, $normalizedOpinions);

        $request = $this->makeFrontendApiRequest(
            method: 'GET',
            uri: FrontendCourseEndpoints::courseOpinionEndpoint(courseId: $course2->getId()),
            bearerToken: $userToken
        );
        $data = $this->extractResponseData($request);
        $normalizedOpinions = $this->normalizeOpinionsData($data);
        $this->assertEquals($expected2, $normalizedOpinions);
    }

    public function testOnsiteCourseOpinions(): void
    {
        $userToken = $this->loginAndGetToken();
        $user = $this->getDefaultUser();
        $userTestOpinion = $this->createAndGetUser(email: self::USER_TEST_OPINION);
        $courseType = $this->getTypeCourse(
            code: TypeCourse::CODE_ONSITE,
        );
        $course = $this->createAndGetCourse(name: 'Onsite Course', code: 'Onsite Course Code', typeCourse: $courseType, description: 'Onsite Course Description');
        $announcement1 = $this->createAndGetAnnouncement($course);
        $announcement2 = $this->createAndGetAnnouncement($course);
        $this->createOpinions(user: $user, text: 'Opinion first announcement', value: '5', announcement: $announcement1);
        $this->createOpinions(user: $userTestOpinion, text: '2 Opinion first announcement', value: '3', announcement: $announcement1);
        $this->createOpinions(user: $user, text: 'Opinion second announcement', value: '7', announcement: $announcement2);
        $this->createOpinions(user: $userTestOpinion, text: '2 Opinion second announcement', value: '2', announcement: $announcement2);

        $expected1 = [
            ['date' => '', 'name' => 'John', 'image' => null, 'rating' => 1.5, 'text' => '2 Opinion first announcement'],
            ['date' => '', 'name' => 'Soporte', 'image' => null, 'rating' => 2.5, 'text' => 'Opinion first announcement'],
        ];

        $expected2 = [
            ['date' => '', 'name' => 'John', 'image' => null, 'rating' => 1, 'text' => '2 Opinion second announcement'],
            ['date' => '', 'name' => 'Soporte', 'image' => null, 'rating' => 3.5, 'text' => 'Opinion second announcement'],
        ];

        $request = $this->makeFrontendApiRequest(
            method: 'GET',
            uri: FrontendCourseEndpoints::courseOpinionEndpoint(courseId: $course->getId(), announcementId: $announcement1->getId()),
            bearerToken: $userToken
        );
        $data = $this->extractResponseData($request);
        $normalizedOpinions = $this->normalizeOpinionsData($data);
        $this->assertEquals($expected1, $normalizedOpinions);

        $request = $this->makeFrontendApiRequest(
            method: 'GET',
            uri: FrontendCourseEndpoints::courseOpinionEndpoint(courseId: $course->getId(), announcementId: $announcement2->getId()),
            bearerToken: $userToken
        );
        $data = $this->extractResponseData($request);
        $normalizedOpinions = $this->normalizeOpinionsData($data);
        $this->assertEquals($expected2, $normalizedOpinions);
    }

    public function createOpinions(User $user, string $text, string $value, ?UserCourse $userCourse = null, ?Announcement $announcement = null): void
    {
        $this->createAndGetOpinion(
            userCourse: $userCourse,
            value: $text,
            user: $user,
            announcement: $announcement,
        );
        $this->createAndGetOpinion(
            userCourse: $userCourse,
            type: 'nps',
            value: $value,
            user: $user,
            announcement: $announcement,
        );
    }

    public function normalizeOpinionsData(array $opinions): array
    {
        foreach ($opinions as &$opinion) {
            $opinion['date'] = '';
        }

        return $opinions;
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Nps::class,
            Course::class,
            UserCourse::class,
            Announcement::class,
        ]);

        $em = $this->getEntityManager();
        $userTestOpinion = $em->getRepository(User::class)->findOneBy(['email' => self::USER_TEST_OPINION]);
        if (!empty($userTestOpinion)) {
            $this->hardDeleteUsersByIds([$userTestOpinion->getId()]);
        }

        parent::tearDown();
    }
}
