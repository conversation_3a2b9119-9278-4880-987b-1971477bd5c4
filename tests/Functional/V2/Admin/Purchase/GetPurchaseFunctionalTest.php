<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminPurchaseEndpoints;
use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
use App\Tests\V2\Mother\Purchase\PurchaseItemMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetPurchaseFunctionalTest extends FunctionalTestCase
{
    use PurchasableItemHelperTrait;

    private PurchaseRepository $purchaseRepository;
    private User $testUser;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        /** @var PurchaseRepository $purchaseRepository */
        $purchaseRepository = $this->getService(PurchaseRepository::class);
        $this->purchaseRepository = $purchaseRepository;

        $this->testUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: 'test.admin.purchase.' . uniqid() . '@example.com',
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([$this->testUser->getId()]);
        }

        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchaseSuccessfully(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchase);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($purchase->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertEquals($purchase->getId()->value(), $data['id']);
        $this->assertEquals($purchase->getUserId()->value(), $data['user_id']);
        $this->assertEquals('completed', $data['status']);
        $this->assertEquals(5000, $data['amount']);
        $this->assertEquals('EUR', $data['currency_code']);
        $this->assertArrayHasKey('tax_rate', $data);
        $this->assertArrayHasKey('tax_amount', $data);
        $this->assertArrayHasKey('created_at', $data);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testGetPurchaseWithDifferentCurrency(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 2500, currency: Currency::USD())
        );

        $this->purchaseRepository->put($purchase);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($purchase->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $data = $responseData['data'];

        $this->assertEquals('pending', $data['status']);
        $this->assertEquals(2500, $data['amount']);
        $this->assertEquals('USD', $data['currency_code']);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testGetPurchaseRequiresAuthentication(): void
    {
        $purchaseId = UuidMother::create();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($purchaseId->value())
            // No bearer token
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testGetPurchaseWithInvalidUuid(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint('invalid-uuid'),
            bearerToken: $userToken
        );

        // Invalid UUIDs can return either 400 (validation error) or 500 (routing/other errors)
        $this->assertContains($response->getStatusCode(), [
            Response::HTTP_BAD_REQUEST,
            Response::HTTP_INTERNAL_SERVER_ERROR,
        ]);
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testGetPurchaseNotFound(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $nonExistentId = UuidMother::create();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($nonExistentId->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
    }

    /**
     * @throws InvalidUuidException
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     * @throws PurchaseRepositoryException
     */
    public function testAdminCanAccessPurchaseFromDifferentUser(): void
    {
        // Create another user
        $otherUser = $this->createAndGetUser(
            email: 'other.user.' . uniqid() . '@example.com',
        );

        $adminToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create purchase owned by the other user
        $purchase = PurchaseMother::create(
            userId: new Id($otherUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 3000)
        );

        $this->purchaseRepository->put($purchase);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($purchase->getId()->value()),
            bearerToken: $adminToken
        );

        // Admin should be able to access any purchase
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertEquals($purchase->getId()->value(), $data['id']);
        $this->assertEquals($otherUser->getId(), $data['user_id']);
        $this->assertEquals('completed', $data['status']);
        $this->assertEquals(3000, $data['amount']);

        // Clean up the other user
        $this->hardDeleteUsersByIds([$otherUser->getId()]);
    }

    #[DataProvider('invalidUuidProvider')]
    public function testGetPurchaseWithVariousInvalidUuids(string $invalidUuid): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($invalidUuid),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws InvalidPurchasableItemException
     * @throws InvalidCurrencyCodeException
     */
    public function testGetPurchaseWithItemsIncluded(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Completed,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );

        $purchasableItem = $this->createAndGetPurchasableItem(
            name: 'Test Course',
            description: 'A test course for purchase',
            priceAmount: 2500,
            priceCurrency: Currency::EUR()
        );

        $purchaseItem = PurchaseItemMother::create(
            purchaseId: $purchase->getId(),
            purchasableItemId: $purchasableItem->getId(),
            price: MoneyMother::create(amount: 2500, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchase);
        $this->purchaseRepository->putPurchaseItem($purchaseItem);

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminPurchaseEndpoints::getPurchaseEndpoint($purchase->getId()->value()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertEquals($purchase->getId()->value(), $data['id']);
        $this->assertEquals($purchase->getUserId()->value(), $data['user_id']);
        $this->assertEquals('completed', $data['status']);
        $this->assertEquals(5000, $data['amount']);
        $this->assertEquals('EUR', $data['currency_code']);

        // Verify that purchase items are included in the response
        $this->assertArrayHasKey('items', $data);
        $this->assertIsArray($data['items']);
        $this->assertCount(1, $data['items']);

        $purchaseItemData = $data['items'][0];
        $this->assertEquals($purchaseItem->getId()->value(), $purchaseItemData['id']);
        $this->assertEquals(2500, $purchaseItemData['price_amount']);
        $this->assertEquals('EUR', $purchaseItemData['price_currency']);

        // Verify that purchasable item details are included (if hydrated)
        $purchasableItemData = $purchaseItemData['purchasable_item'];
        $this->assertEquals($purchasableItem->getId()->value(), $purchasableItemData['id']);
        $this->assertEquals('Test Course', $purchasableItemData['name']);
        $this->assertEquals('A test course for purchase', $purchasableItemData['description']);
        $this->assertEquals(2500, $purchasableItemData['price_amount']);
        $this->assertEquals('EUR', $purchasableItemData['price_currency']);
    }

    public static function invalidUuidProvider(): \Generator
    {
        yield 'too short' => ['123'];
        yield 'too long' => ['550e8400-e29b-41d4-a716-************-extra'];
        yield 'invalid characters' => ['550e8400-e29b-41d4-a716-44665544000g'];
        yield 'missing hyphens' => ['550e8400e29b41d4a716************'];
        yield 'wrong format' => ['550e8400-e29b-41d4-a716'];
        yield 'null string' => ['null'];
        yield 'boolean string' => ['true'];
    }
}
