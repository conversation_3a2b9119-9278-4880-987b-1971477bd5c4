<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\HigherOrLower;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\HigherLower;
use Doctrine\ORM\EntityManagerInterface;

class GameHigherOrLowerStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new HigherOrLower($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);
        $question1 = new HigherLower();
        $question2 = new HigherLower();
        $question1->setChapter($chapter);
        $question2->setChapter($chapter);
        $chapter->addHigherLower($question1);
        $chapter->addHigherLower($question2);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'attempt' => [
                    [
                        'id' => 1,
                        'correct' => false,
                    ],
                    [
                        'id' => 2,
                        'correct' => false,
                    ],
                    [
                        'id' => 3,
                        'correct' => false,
                    ],
                ],
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'words' => 3,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko No correct answers.' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'totalQuestions' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko no correct answers without attempts' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 10,
                'correct' => false,
                'attempt' => [
                    [
                        'id' => 1,
                        'correct' => true,
                    ],
                    [
                        'id' => 2,
                        'correct' => true,
                    ],
                    [
                        'id' => 3,
                        'correct' => true,
                    ],
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => false,
                'attempt' => [
                    [
                        'id' => 1,
                        'correct' => true,
                    ],
                    [
                        'id' => 2,
                        'correct' => true,
                    ],
                    [
                        'id' => 3,
                        'correct' => true,
                    ],
                ],
            ],
        ];
        yield 'result ok. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'totalQuestions' => 2,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.8532,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'attempt' => [
                    [
                        'id' => 1,
                        'correct' => true,
                    ],
                    [
                        'id' => 2,
                        'correct' => true,
                    ],
                    [
                        'id' => 3,
                        'correct' => true,
                    ],
                ],
            ],
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'attempt' => [
                    [
                        'id' => 1,
                        'correct' => false,
                    ],
                    [
                        'id' => 2,
                        'correct' => false,
                    ],
                    [
                        'id' => 3,
                        'correct' => true,
                    ],
                ],
            ],
        ];

        yield 'result ko. not completed percentages (2 answers: 1 correct and 1 fail)' => [
            'data' => ['answers' => $answers,
                'words' => 3,
                'totalQuestions' => 2,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];
    }
}
